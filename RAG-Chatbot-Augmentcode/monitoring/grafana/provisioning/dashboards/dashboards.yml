apiVersion: 1

providers:
  # System dashboards
  - name: 'system'
    orgId: 1
    folder: 'System'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/system

  # Application dashboards
  - name: 'application'
    orgId: 1
    folder: 'Application'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/application

  # Database dashboards
  - name: 'database'
    orgId: 1
    folder: 'Database'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/database

  # Business dashboards
  - name: 'business'
    orgId: 1
    folder: 'Business'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/business
