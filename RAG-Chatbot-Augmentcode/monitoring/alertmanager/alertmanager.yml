global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-email-password'
  smtp_require_tls: true

# Templates for notifications
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Route tree for alerts
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  routes:
    # Critical alerts - immediate notification
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m
      routes:
        - match:
            component: redis
          receiver: 'database-team'
        - match:
            component: milvus
          receiver: 'database-team'
        - match:
            component: api
          receiver: 'backend-team'
        - match:
            component: worker
          receiver: 'backend-team'

    # Warning alerts - less frequent notifications
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 30s
      repeat_interval: 30m

    # Security alerts - special handling
    - match:
        type: security
      receiver: 'security-team'
      group_wait: 0s
      repeat_interval: 15m

    # Business alerts - business hours only
    - match:
        type: business
      receiver: 'business-team'
      group_wait: 5m
      repeat_interval: 2h

    # Info alerts - daily digest
    - match:
        severity: info
      receiver: 'info-digest'
      group_wait: 1h
      repeat_interval: 24h

# Inhibition rules to reduce noise
inhibit_rules:
  # Inhibit warning alerts if critical alert is firing
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']

  # Inhibit individual service alerts if general service down alert is firing
  - source_match:
      alertname: 'ServiceDown'
    target_match_re:
      alertname: '.*High.*|.*Slow.*'
    equal: ['instance']

# Receivers define how to send notifications
receivers:
  # Default receiver
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: '[RAG-Chatbot] Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}

  # Critical alerts - multiple channels
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[CRITICAL] RAG-Chatbot Alert: {{ .GroupLabels.alertname }}'
        body: |
          🚨 CRITICAL ALERT 🚨
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          Started: {{ .StartsAt }}
          {{ end }}
          
          Please investigate immediately!
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#alerts-critical'
        title: '🚨 Critical Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Service:* {{ .Labels.service }}
          {{ end }}
        send_resolved: true
    webhook_configs:
      - url: 'http://your-webhook-endpoint/critical'
        send_resolved: true

  # Warning alerts
  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[WARNING] RAG-Chatbot Alert: {{ .GroupLabels.alertname }}'
        body: |
          ⚠️ WARNING ALERT ⚠️
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Started: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#alerts-warning'
        title: '⚠️ Warning: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          {{ end }}

  # Database team alerts
  - name: 'database-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '[DB-ALERT] RAG-Chatbot Database Issue: {{ .GroupLabels.alertname }}'
        body: |
          🗄️ DATABASE ALERT 🗄️
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Component: {{ .Labels.component }}
          Instance: {{ .Labels.instance }}
          Started: {{ .StartsAt }}
          {{ end }}

  # Backend team alerts
  - name: 'backend-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '[API-ALERT] RAG-Chatbot Backend Issue: {{ .GroupLabels.alertname }}'
        body: |
          🔧 BACKEND ALERT 🔧
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Component: {{ .Labels.component }}
          Instance: {{ .Labels.instance }}
          Started: {{ .StartsAt }}
          {{ end }}

  # Security team alerts
  - name: 'security-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '[SECURITY] RAG-Chatbot Security Alert: {{ .GroupLabels.alertname }}'
        body: |
          🔒 SECURITY ALERT 🔒
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Type: {{ .Labels.type }}
          Instance: {{ .Labels.instance }}
          Started: {{ .StartsAt }}
          {{ end }}
          
          Immediate investigation required!
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#security-alerts'
        title: '🔒 Security Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          {{ end }}

  # Business team alerts
  - name: 'business-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '[BUSINESS] RAG-Chatbot Business Metric Alert: {{ .GroupLabels.alertname }}'
        body: |
          📊 BUSINESS ALERT 📊
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Type: {{ .Labels.type }}
          Started: {{ .StartsAt }}
          {{ end }}

  # Info digest - daily summary
  - name: 'info-digest'
    email_configs:
      - to: '<EMAIL>'
        subject: '[INFO] RAG-Chatbot Daily Digest'
        body: |
          📈 DAILY DIGEST 📈
          
          {{ range .Alerts }}
          Info: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}

# Silence configuration
# silences:
#   - matchers:
#       - name: alertname
#         value: MaintenanceMode
#     startsAt: "2024-01-01T00:00:00Z"
#     endsAt: "2024-01-01T06:00:00Z"
#     createdBy: "ops-team"
#     comment: "Scheduled maintenance window"
