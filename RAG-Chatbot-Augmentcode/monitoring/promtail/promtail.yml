server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # Docker container logs
  - job_name: containers
    static_configs:
      - targets:
          - localhost
        labels:
          job: containerlogs
          __path__: /var/lib/docker/containers/*/*log

    pipeline_stages:
      - json:
          expressions:
            output: log
            stream: stream
            attrs:
      - json:
          expressions:
            tag:
          source: attrs
      - regex:
          expression: (?P<container_name>(?:[^|]*))\|
          source: tag
      - timestamp:
          format: RFC3339Nano
          source: time
      - labels:
          stream:
          container_name:
      - output:
          source: output

  # System logs
  - job_name: syslog
    static_configs:
      - targets:
          - localhost
        labels:
          job: syslog
          __path__: /var/log/syslog

  # Application logs
  - job_name: rag-chatbot-api
    static_configs:
      - targets:
          - localhost
        labels:
          job: rag-chatbot-api
          component: api
          __path__: /var/log/rag-chatbot/api/*.log

    pipeline_stages:
      - json:
          expressions:
            timestamp: timestamp
            level: level
            message: message
            module: module
            request_id: request_id
      - timestamp:
          format: RFC3339
          source: timestamp
      - labels:
          level:
          module:
          component:

  - job_name: rag-chatbot-worker
    static_configs:
      - targets:
          - localhost
        labels:
          job: rag-chatbot-worker
          component: worker
          __path__: /var/log/rag-chatbot/worker/*.log

    pipeline_stages:
      - json:
          expressions:
            timestamp: timestamp
            level: level
            message: message
            task: task
            worker: worker
      - timestamp:
          format: RFC3339
          source: timestamp
      - labels:
          level:
          task:
          worker:
          component:

  # Nginx access logs
  - job_name: nginx-access
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx-access
          component: nginx
          __path__: /var/log/nginx/access.log

    pipeline_stages:
      - regex:
          expression: '^(?P<remote_addr>\S+) - (?P<remote_user>\S+) \[(?P<time_local>[^\]]+)\] "(?P<method>\S+) (?P<path>\S+) (?P<protocol>\S+)" (?P<status>\d+) (?P<body_bytes_sent>\d+) "(?P<http_referer>[^"]*)" "(?P<http_user_agent>[^"]*)"'
      - timestamp:
          format: 02/Jan/2006:15:04:05 -0700
          source: time_local
      - labels:
          method:
          status:
          component:

  # Nginx error logs
  - job_name: nginx-error
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx-error
          component: nginx
          __path__: /var/log/nginx/error.log

    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}) \[(?P<level>\w+)\] (?P<message>.*)'
      - timestamp:
          format: 2006/01/02 15:04:05
          source: timestamp
      - labels:
          level:
          component:

  # Redis logs
  - job_name: redis
    static_configs:
      - targets:
          - localhost
        labels:
          job: redis
          component: redis
          __path__: /var/log/redis/*.log

    pipeline_stages:
      - regex:
          expression: '^(?P<pid>\d+):(?P<role>\w+) (?P<timestamp>\d{2} \w{3} \d{4} \d{2}:\d{2}:\d{2}\.\d{3}) (?P<level>\w+) (?P<message>.*)'
      - timestamp:
          format: 02 Jan 2006 15:04:05.000
          source: timestamp
      - labels:
          level:
          role:
          component:
