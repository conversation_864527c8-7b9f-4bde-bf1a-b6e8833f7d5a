version: '3.8'

services:
  # Redis for caching and task queue
  redis-test:
    image: redis:7.2-alpine
    container_name: rag-redis-test
    ports:
      - "6380:6379"  # Use different port to avoid conflicts
    volumes:
      - redis_test_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # MinIO for Milvus storage
  minio-test:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    container_name: milvus-minio-test
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9002:9000"  # Use different port
      - "9003:9001"  # Use different port
    volumes:
      - minio_test_data:/data
    command: minio server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped

  # etcd for Milvus metadata
  etcd-test:
    image: quay.io/coreos/etcd:v3.5.5
    container_name: milvus-etcd-test
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_test_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped

  # Milvus vector database
  milvus-test:
    image: milvusdb/milvus:v2.3.0
    container_name: milvus-standalone-test
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd-test:2379
      MINIO_ADDRESS: minio-test:9000
    volumes:
      - milvus_test_data:/var/lib/milvus
    ports:
      - "19531:19530"  # Use different port
      - "9092:9091"    # Use different port
    depends_on:
      etcd-test:
        condition: service_healthy
      minio-test:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      timeout: 20s
      retries: 5
      start_period: 90s
    restart: unless-stopped

volumes:
  redis_test_data:
  minio_test_data:
  etcd_test_data:
  milvus_test_data:
