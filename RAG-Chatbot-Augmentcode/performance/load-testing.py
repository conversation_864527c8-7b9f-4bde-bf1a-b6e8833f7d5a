#!/usr/bin/env python3
"""
Load Testing Suite for RAG Legal Chatbot
Comprehensive performance testing with Locust
"""

import json
import random
import time
from typing import Dict, List, Any
from locust import HttpUser, task, between, events
from locust.runners import <PERSON><PERSON><PERSON>ner, WorkerRunner
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChatbotUser(HttpUser):
    """Simulates a user interacting with the RAG Legal Chatbot"""
    
    wait_time = between(1, 5)  # Wait 1-5 seconds between requests
    
    def on_start(self):
        """Initialize user session"""
        self.session_id = f"session_{random.randint(1000, 9999)}"
        self.conversation_history = []
        
        # Sample legal questions for testing
        self.legal_questions = [
            "What are the requirements for forming a limited liability company?",
            "How long does a copyright last in Germany?",
            "What is the difference between a trademark and a patent?",
            "What are the legal requirements for employment contracts?",
            "How can I protect my intellectual property?",
            "What are the steps to register a business in Germany?",
            "What are the tax implications of selling a property?",
            "How do I handle a breach of contract situation?",
            "What are the legal requirements for data protection?",
            "How can I resolve a commercial dispute?",
            "What are the employment law requirements for termination?",
            "How do I protect my trade secrets?",
            "What are the legal aspects of e-commerce?",
            "How can I comply with GDPR regulations?",
            "What are the requirements for corporate governance?"
        ]
        
        # Document types for upload testing
        self.document_types = [
            "contract", "legal_opinion", "court_decision", 
            "regulation", "statute", "case_law"
        ]
    
    @task(10)
    def health_check(self):
        """Test health endpoint - high frequency"""
        with self.client.get("/health", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(30)
    def ask_question(self):
        """Test chat endpoint with legal questions"""
        question = random.choice(self.legal_questions)
        
        payload = {
            "query": question,
            "session_id": self.session_id,
            "max_results": 5,
            "include_sources": True
        }
        
        with self.client.post(
            "/api/chat",
            json=payload,
            headers={"Content-Type": "application/json"},
            catch_response=True,
            name="chat_query"
        ) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    if "answer" in data and "sources" in data:
                        response.success()
                        self.conversation_history.append({
                            "question": question,
                            "answer": data["answer"][:100] + "...",
                            "sources_count": len(data.get("sources", []))
                        })
                    else:
                        response.failure("Invalid response format")
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"Chat query failed: {response.status_code}")
    
    @task(15)
    def stream_chat(self):
        """Test streaming chat endpoint"""
        question = random.choice(self.legal_questions)
        
        payload = {
            "query": question,
            "session_id": self.session_id,
            "stream": True
        }
        
        with self.client.post(
            "/api/chat/stream",
            json=payload,
            headers={"Content-Type": "application/json"},
            stream=True,
            catch_response=True,
            name="chat_stream"
        ) as response:
            if response.status_code == 200:
                chunks_received = 0
                try:
                    for line in response.iter_lines():
                        if line:
                            chunks_received += 1
                            if chunks_received > 100:  # Prevent infinite loops
                                break
                    
                    if chunks_received > 0:
                        response.success()
                    else:
                        response.failure("No streaming chunks received")
                except Exception as e:
                    response.failure(f"Streaming error: {str(e)}")
            else:
                response.failure(f"Stream chat failed: {response.status_code}")
    
    @task(5)
    def search_documents(self):
        """Test document search endpoint"""
        search_terms = [
            "contract law", "intellectual property", "employment rights",
            "data protection", "commercial law", "corporate governance"
        ]
        
        payload = {
            "query": random.choice(search_terms),
            "limit": 10,
            "filters": {
                "document_type": random.choice(self.document_types)
            }
        }
        
        with self.client.post(
            "/api/search",
            json=payload,
            headers={"Content-Type": "application/json"},
            catch_response=True,
            name="document_search"
        ) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    if "results" in data:
                        response.success()
                    else:
                        response.failure("Invalid search response format")
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"Document search failed: {response.status_code}")
    
    @task(3)
    def get_conversation_history(self):
        """Test conversation history endpoint"""
        with self.client.get(
            f"/api/conversations/{self.session_id}",
            catch_response=True,
            name="conversation_history"
        ) as response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    if "conversations" in data:
                        response.success()
                    else:
                        response.failure("Invalid history response format")
                except json.JSONDecodeError:
                    response.failure("Invalid JSON response")
            else:
                response.failure(f"Conversation history failed: {response.status_code}")
    
    @task(2)
    def admin_stats(self):
        """Test admin statistics endpoint"""
        with self.client.get(
            "/api/admin/stats",
            headers={"Authorization": "Bearer admin_test_token"},
            catch_response=True,
            name="admin_stats"
        ) as response:
            if response.status_code in [200, 401]:  # 401 is expected without proper auth
                response.success()
            else:
                response.failure(f"Admin stats failed: {response.status_code}")
    
    @task(1)
    def metrics_endpoint(self):
        """Test metrics endpoint"""
        with self.client.get(
            "/metrics",
            catch_response=True,
            name="metrics"
        ) as response:
            if response.status_code == 200:
                if "# HELP" in response.text:  # Prometheus metrics format
                    response.success()
                else:
                    response.failure("Invalid metrics format")
            else:
                response.failure(f"Metrics failed: {response.status_code}")

class HighVolumeUser(ChatbotUser):
    """High-volume user for stress testing"""
    
    wait_time = between(0.1, 1)  # Faster requests
    
    @task(50)
    def rapid_fire_questions(self):
        """Rapid fire questions for stress testing"""
        question = random.choice(self.legal_questions)
        
        payload = {
            "query": question,
            "session_id": self.session_id,
            "max_results": 3  # Fewer results for faster response
        }
        
        with self.client.post(
            "/api/chat",
            json=payload,
            headers={"Content-Type": "application/json"},
            catch_response=True,
            name="rapid_chat"
        ) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Rapid chat failed: {response.status_code}")

class AdminUser(HttpUser):
    """Admin user for testing administrative endpoints"""
    
    wait_time = between(5, 15)
    weight = 1  # Lower weight for admin users
    
    def on_start(self):
        """Admin user initialization"""
        self.admin_token = "admin_test_token"
    
    @task(10)
    def admin_dashboard(self):
        """Test admin dashboard"""
        with self.client.get(
            "/api/admin/dashboard",
            headers={"Authorization": f"Bearer {self.admin_token}"},
            catch_response=True,
            name="admin_dashboard"
        ) as response:
            if response.status_code in [200, 401]:
                response.success()
            else:
                response.failure(f"Admin dashboard failed: {response.status_code}")
    
    @task(5)
    def system_health(self):
        """Test system health check"""
        with self.client.get(
            "/api/admin/health",
            headers={"Authorization": f"Bearer {self.admin_token}"},
            catch_response=True,
            name="system_health"
        ) as response:
            if response.status_code in [200, 401]:
                response.success()
            else:
                response.failure(f"System health failed: {response.status_code}")

# Performance monitoring events
@events.request.add_listener
def on_request(request_type, name, response_time, response_length, exception, context, **kwargs):
    """Log slow requests"""
    if response_time > 2000:  # Log requests slower than 2 seconds
        logger.warning(f"Slow request: {name} took {response_time}ms")

@events.user_error.add_listener
def on_user_error(user_instance, exception, tb, **kwargs):
    """Log user errors"""
    logger.error(f"User error: {exception}")

@events.init.add_listener
def on_locust_init(environment, **kwargs):
    """Initialize load testing environment"""
    if isinstance(environment.runner, MasterRunner):
        logger.info("Starting Locust master")
    elif isinstance(environment.runner, WorkerRunner):
        logger.info("Starting Locust worker")
    else:
        logger.info("Starting Locust standalone")

# Custom performance test scenarios
class PerformanceTestSuite:
    """Custom performance test scenarios"""
    
    @staticmethod
    def spike_test():
        """Simulate traffic spikes"""
        # This would be implemented as a custom test scenario
        pass
    
    @staticmethod
    def endurance_test():
        """Long-running endurance test"""
        # This would be implemented as a custom test scenario
        pass
    
    @staticmethod
    def capacity_test():
        """Find system capacity limits"""
        # This would be implemented as a custom test scenario
        pass

if __name__ == "__main__":
    # Example usage for running tests programmatically
    import subprocess
    import sys
    
    def run_load_test(users=10, spawn_rate=2, duration="5m", host="http://localhost:8000"):
        """Run load test with specified parameters"""
        cmd = [
            "locust",
            "-f", __file__,
            "--users", str(users),
            "--spawn-rate", str(spawn_rate),
            "--run-time", duration,
            "--host", host,
            "--headless",
            "--html", "performance_report.html",
            "--csv", "performance_results"
        ]
        
        logger.info(f"Running load test: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("Load test completed successfully")
            print(result.stdout)
        else:
            logger.error(f"Load test failed: {result.stderr}")
            sys.exit(1)
    
    # Default test configuration
    run_load_test(
        users=50,
        spawn_rate=5,
        duration="10m",
        host="http://localhost:8000"
    )
