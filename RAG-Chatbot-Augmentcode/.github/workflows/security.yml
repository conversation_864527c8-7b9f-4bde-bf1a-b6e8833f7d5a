name: Security Scanning

on:
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

permissions:
  contents: read
  security-events: write
  actions: read

jobs:
  # CodeQL Analysis
  codeql:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        language: [ 'python', 'javascript' ]
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: ${{ matrix.language }}
          queries: security-extended,security-and-quality

      - name: Autobuild
        uses: github/codeql-action/autobuild@v2

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          category: "/language:${{matrix.language}}"

  # Dependency Scanning
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install safety bandit semgrep

      - name: Run Safety check
        run: |
          safety check --json --output safety-report.json || true
          safety check --short-report

      - name: Run Bandit security scan
        run: |
          bandit -r chatbot-engine/src/ -f json -o bandit-report.json || true
          bandit -r chatbot-engine/src/ -f txt

      - name: Run Semgrep scan
        run: |
          semgrep --config=auto --json --output=semgrep-report.json chatbot-engine/src/ || true
          semgrep --config=auto chatbot-engine/src/

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            safety-report.json
            bandit-report.json
            semgrep-report.json

  # Container Security Scanning
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    if: github.event_name != 'schedule'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build API image for scanning
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./chatbot-engine/docker/Dockerfile.api
          target: production
          tags: rag-chatbot-api:scan
          load: true

      - name: Build Worker image for scanning
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./chatbot-engine/docker/Dockerfile.worker
          target: production
          tags: rag-chatbot-worker:scan
          load: true

      - name: Run Trivy vulnerability scanner (API)
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'rag-chatbot-api:scan'
          format: 'sarif'
          output: 'trivy-api-results.sarif'

      - name: Run Trivy vulnerability scanner (Worker)
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'rag-chatbot-worker:scan'
          format: 'sarif'
          output: 'trivy-worker-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-api-results.sarif'

      - name: Upload Worker Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-worker-results.sarif'

      - name: Run Grype vulnerability scanner
        run: |
          # Install Grype
          curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sh -s -- -b /usr/local/bin
          
          # Scan API image
          grype rag-chatbot-api:scan -o json > grype-api-report.json
          grype rag-chatbot-api:scan
          
          # Scan Worker image
          grype rag-chatbot-worker:scan -o json > grype-worker-report.json
          grype rag-chatbot-worker:scan

      - name: Upload Grype reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: grype-reports
          path: |
            grype-api-report.json
            grype-worker-report.json

  # Infrastructure Security Scanning
  infrastructure-scan:
    name: Infrastructure Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Checkov scan
        uses: bridgecrewio/checkov-action@master
        with:
          directory: .
          framework: dockerfile,kubernetes,docker_compose
          output_format: sarif
          output_file_path: checkov-report.sarif

      - name: Upload Checkov scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: checkov-report.sarif

      - name: Run Terrascan
        run: |
          # Install Terrascan
          curl -L "$(curl -s https://api.github.com/repos/tenable/terrascan/releases/latest | grep -o -E "https://.+?_Linux_x86_64.tar.gz")" > terrascan.tar.gz
          tar -xf terrascan.tar.gz terrascan && rm terrascan.tar.gz
          sudo mv terrascan /usr/local/bin
          
          # Scan Kubernetes manifests
          terrascan scan -t k8s -d k8s/ -o json > terrascan-k8s-report.json || true
          terrascan scan -t k8s -d k8s/
          
          # Scan Docker files
          terrascan scan -t docker -d . -o json > terrascan-docker-report.json || true
          terrascan scan -t docker -d .

      - name: Upload Terrascan reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: terrascan-reports
          path: |
            terrascan-k8s-report.json
            terrascan-docker-report.json

  # Secrets Scanning
  secrets-scan:
    name: Secrets Scanning
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run TruffleHog
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

      - name: Run GitLeaks
        run: |
          # Install GitLeaks
          curl -sSfL https://github.com/zricethezav/gitleaks/releases/latest/download/gitleaks_linux_x64.tar.gz | tar xz
          sudo mv gitleaks /usr/local/bin
          
          # Scan for secrets
          gitleaks detect --source . --report-format json --report-path gitleaks-report.json || true
          gitleaks detect --source . --verbose

      - name: Upload GitLeaks report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: gitleaks-report
          path: gitleaks-report.json

  # License Compliance
  license-scan:
    name: License Compliance Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pip-licenses licensecheck

      - name: Generate license report
        run: |
          # Install project dependencies
          pip install -r requirements.txt
          
          # Generate license report
          pip-licenses --format=json --output-file=licenses.json
          pip-licenses --format=csv --output-file=licenses.csv
          pip-licenses

      - name: Check for problematic licenses
        run: |
          # Check for GPL and other copyleft licenses
          if pip-licenses | grep -E "(GPL|AGPL|LGPL)"; then
            echo "⚠️ Found copyleft licenses that may require attention"
          else
            echo "✅ No problematic licenses found"
          fi

      - name: Upload license reports
        uses: actions/upload-artifact@v3
        with:
          name: license-reports
          path: |
            licenses.json
            licenses.csv

  # Security Summary
  security-summary:
    name: Security Summary
    runs-on: ubuntu-latest
    needs: [codeql, dependency-scan, container-scan, infrastructure-scan, secrets-scan, license-scan]
    if: always()
    
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v3

      - name: Generate security summary
        run: |
          echo "# Security Scan Summary" > security-summary.md
          echo "Generated: $(date)" >> security-summary.md
          echo "" >> security-summary.md
          
          echo "## Scan Results" >> security-summary.md
          echo "- CodeQL: ${{ needs.codeql.result }}" >> security-summary.md
          echo "- Dependency Scan: ${{ needs.dependency-scan.result }}" >> security-summary.md
          echo "- Container Scan: ${{ needs.container-scan.result }}" >> security-summary.md
          echo "- Infrastructure Scan: ${{ needs.infrastructure-scan.result }}" >> security-summary.md
          echo "- Secrets Scan: ${{ needs.secrets-scan.result }}" >> security-summary.md
          echo "- License Scan: ${{ needs.license-scan.result }}" >> security-summary.md
          echo "" >> security-summary.md
          
          # Count vulnerabilities if reports exist
          if [ -f "security-reports/safety-report.json" ]; then
            SAFETY_VULNS=$(jq '.vulnerabilities | length' security-reports/safety-report.json 2>/dev/null || echo "0")
            echo "- Safety vulnerabilities: $SAFETY_VULNS" >> security-summary.md
          fi
          
          if [ -f "security-reports/bandit-report.json" ]; then
            BANDIT_ISSUES=$(jq '.results | length' security-reports/bandit-report.json 2>/dev/null || echo "0")
            echo "- Bandit security issues: $BANDIT_ISSUES" >> security-summary.md
          fi

      - name: Upload security summary
        uses: actions/upload-artifact@v3
        with:
          name: security-summary
          path: security-summary.md

      - name: Comment on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const summary = fs.readFileSync('security-summary.md', 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: summary
            });
