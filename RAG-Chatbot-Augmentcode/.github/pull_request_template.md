## Description

Brief description of the changes in this PR.

## Type of Change

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring
- [ ] Security fix
- [ ] Infrastructure/DevOps change

## Related Issues

Fixes #(issue number)
Closes #(issue number)
Related to #(issue number)

## Changes Made

- [ ] Change 1
- [ ] Change 2
- [ ] Change 3

## Testing

- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] End-to-end tests added/updated
- [ ] Manual testing completed
- [ ] Performance testing completed (if applicable)

### Test Coverage

- Current coverage: X%
- Coverage change: +/-X%

## Security Considerations

- [ ] No sensitive data exposed
- [ ] Input validation implemented
- [ ] Authentication/authorization checked
- [ ] Security scan passed
- [ ] Dependencies updated and scanned

## Performance Impact

- [ ] No performance impact
- [ ] Performance improved
- [ ] Performance impact assessed and acceptable
- [ ] Load testing completed (if applicable)

## Documentation

- [ ] Code comments updated
- [ ] README updated
- [ ] API documentation updated
- [ ] Deployment documentation updated
- [ ] User documentation updated

## Deployment

- [ ] Database migrations included (if applicable)
- [ ] Environment variables updated (if applicable)
- [ ] Configuration changes documented
- [ ] Backward compatibility maintained
- [ ] Rollback plan documented

## Checklist

- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is properly commented
- [ ] Tests pass locally
- [ ] No merge conflicts
- [ ] Branch is up to date with main
- [ ] Commit messages are clear and descriptive

## Screenshots (if applicable)

Add screenshots or GIFs to help explain your changes.

## Additional Notes

Any additional information that reviewers should know.

## Reviewer Guidelines

Please ensure:
- [ ] Code quality and style
- [ ] Test coverage is adequate
- [ ] Security considerations addressed
- [ ] Performance impact assessed
- [ ] Documentation is complete
- [ ] Breaking changes are clearly marked
