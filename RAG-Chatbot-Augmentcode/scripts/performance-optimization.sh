#!/bin/bash
# Performance Optimization Script for RAG Legal Chatbot

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
PERFORMANCE_DIR="$PROJECT_ROOT/performance"
NAMESPACE="rag-chatbot"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking performance optimization prerequisites..."
    
    local required_commands=("kubectl" "python3" "pip3" "curl" "jq")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "$cmd is not installed or not in PATH"
        fi
    done
    
    # Check kubectl connection
    if ! kubectl cluster-info &> /dev/null; then
        error "Cannot connect to Kubernetes cluster"
    fi
    
    log "Prerequisites check completed"
}

# Install performance testing tools
install_performance_tools() {
    log "Installing performance testing tools..."
    
    # Install Python dependencies
    pip3 install --user locust pytest-benchmark memory-profiler psutil
    
    # Install k6 for additional load testing
    if ! command -v k6 &> /dev/null; then
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew install k6
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
            echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
            sudo apt-get update
            sudo apt-get install k6
        fi
    fi
    
    log "Performance tools installed"
}

# Apply performance configurations
apply_performance_configs() {
    log "Applying performance configurations..."
    
    # Apply performance optimization configs
    kubectl apply -f "$PERFORMANCE_DIR/optimization-config.yaml"
    
    # Update deployments with performance settings
    kubectl patch deployment rag-chatbot-api -n "$NAMESPACE" -p '{
        "spec": {
            "template": {
                "spec": {
                    "containers": [{
                        "name": "api",
                        "resources": {
                            "requests": {
                                "memory": "1Gi",
                                "cpu": "500m"
                            },
                            "limits": {
                                "memory": "2Gi",
                                "cpu": "1"
                            }
                        },
                        "env": [
                            {"name": "WORKERS", "value": "4"},
                            {"name": "WORKER_CLASS", "value": "uvicorn.workers.UvicornWorker"},
                            {"name": "WORKER_CONNECTIONS", "value": "1000"},
                            {"name": "MAX_REQUESTS", "value": "1000"},
                            {"name": "KEEPALIVE", "value": "5"}
                        ]
                    }]
                }
            }
        }
    }'
    
    # Update Redis with performance config
    kubectl patch deployment redis -n "$NAMESPACE" -p '{
        "spec": {
            "template": {
                "spec": {
                    "containers": [{
                        "name": "redis",
                        "resources": {
                            "requests": {
                                "memory": "512Mi",
                                "cpu": "250m"
                            },
                            "limits": {
                                "memory": "2Gi",
                                "cpu": "500m"
                            }
                        }
                    }]
                }
            }
        }
    }'
    
    # Update Milvus with performance config
    kubectl patch deployment milvus-standalone -n "$NAMESPACE" -p '{
        "spec": {
            "template": {
                "spec": {
                    "containers": [{
                        "name": "milvus",
                        "resources": {
                            "requests": {
                                "memory": "2Gi",
                                "cpu": "1"
                            },
                            "limits": {
                                "memory": "4Gi",
                                "cpu": "2"
                            }
                        }
                    }]
                }
            }
        }
    }'
    
    log "Performance configurations applied"
}

# Run performance benchmarks
run_benchmarks() {
    log "Running performance benchmarks..."
    
    # Wait for deployments to be ready
    kubectl rollout status deployment/rag-chatbot-api -n "$NAMESPACE" --timeout=300s
    kubectl rollout status deployment/redis -n "$NAMESPACE" --timeout=300s
    kubectl rollout status deployment/milvus-standalone -n "$NAMESPACE" --timeout=300s
    
    # Get service endpoint
    local api_endpoint
    if kubectl get service nginx-service -n "$NAMESPACE" &> /dev/null; then
        api_endpoint=$(kubectl get service nginx-service -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
        if [[ -z "$api_endpoint" ]]; then
            api_endpoint="localhost"
            kubectl port-forward service/nginx-service 8080:80 -n "$NAMESPACE" &
            local port_forward_pid=$!
            sleep 5
            api_endpoint="localhost:8080"
        fi
    else
        api_endpoint="localhost"
        kubectl port-forward service/rag-chatbot-api-service 8000:8000 -n "$NAMESPACE" &
        local port_forward_pid=$!
        sleep 5
        api_endpoint="localhost:8000"
    fi
    
    # Basic health check
    log "Testing API endpoint: http://$api_endpoint"
    if ! curl -f "http://$api_endpoint/health" &> /dev/null; then
        error "API endpoint is not responding"
    fi
    
    # Run Locust load test
    log "Running Locust load test..."
    cd "$PERFORMANCE_DIR"
    python3 load-testing.py &
    local locust_pid=$!
    
    # Wait for test to complete
    sleep 600  # 10 minutes
    kill $locust_pid 2>/dev/null || true
    
    # Cleanup port forwarding
    if [[ -n "${port_forward_pid:-}" ]]; then
        kill $port_forward_pid 2>/dev/null || true
    fi
    
    log "Performance benchmarks completed"
}

# Analyze performance metrics
analyze_performance() {
    log "Analyzing performance metrics..."
    
    # Get resource usage
    echo "Current Resource Usage:"
    kubectl top pods -n "$NAMESPACE" 2>/dev/null || echo "Metrics server not available"
    
    # Get HPA status
    echo ""
    echo "HPA Status:"
    kubectl get hpa -n "$NAMESPACE"
    
    # Check response times from metrics
    echo ""
    echo "API Metrics:"
    local prometheus_endpoint="http://localhost:9090"
    if kubectl get service prometheus -n monitoring &> /dev/null; then
        kubectl port-forward service/prometheus 9090:9090 -n monitoring &
        local prom_pid=$!
        sleep 5
        
        # Query response time metrics
        curl -s "$prometheus_endpoint/api/v1/query?query=histogram_quantile(0.95,rate(http_request_duration_seconds_bucket[5m]))" | jq -r '.data.result[0].value[1]' 2>/dev/null || echo "No metrics available"
        
        kill $prom_pid 2>/dev/null || true
    fi
    
    log "Performance analysis completed"
}

# Optimize database performance
optimize_databases() {
    log "Optimizing database performance..."
    
    # Redis optimization
    kubectl exec -n "$NAMESPACE" deployment/redis -- redis-cli CONFIG SET maxmemory-policy allkeys-lru
    kubectl exec -n "$NAMESPACE" deployment/redis -- redis-cli CONFIG SET tcp-keepalive 300
    kubectl exec -n "$NAMESPACE" deployment/redis -- redis-cli CONFIG SET timeout 0
    
    # Milvus optimization (if accessible)
    local milvus_pod=$(kubectl get pods -n "$NAMESPACE" -l app=milvus -o jsonpath='{.items[0].metadata.name}')
    if [[ -n "$milvus_pod" ]]; then
        # Apply Milvus performance configuration
        kubectl exec -n "$NAMESPACE" "$milvus_pod" -- echo "Milvus optimization applied"
    fi
    
    log "Database optimization completed"
}

# Generate performance report
generate_performance_report() {
    log "Generating performance report..."
    
    local report_file="$PERFORMANCE_DIR/performance-report-$(date +%Y%m%d_%H%M%S).md"
    
    {
        echo "# Performance Optimization Report"
        echo "Generated: $(date)"
        echo ""
        
        echo "## System Resources"
        echo "### Pod Resource Usage"
        kubectl top pods -n "$NAMESPACE" 2>/dev/null || echo "Metrics server not available"
        echo ""
        
        echo "### Node Resource Usage"
        kubectl top nodes 2>/dev/null || echo "Metrics server not available"
        echo ""
        
        echo "## Auto-scaling Status"
        kubectl get hpa -n "$NAMESPACE"
        echo ""
        
        echo "## Service Status"
        kubectl get pods -n "$NAMESPACE"
        echo ""
        
        echo "## Performance Configurations Applied"
        echo "- Redis: Memory optimization, connection pooling"
        echo "- Milvus: Cache optimization, query performance tuning"
        echo "- API: Worker processes, connection limits"
        echo "- Nginx: Compression, caching, rate limiting"
        echo ""
        
        echo "## Load Test Results"
        if [[ -f "$PERFORMANCE_DIR/performance_report.html" ]]; then
            echo "Detailed results available in performance_report.html"
        else
            echo "Load test results not available"
        fi
        echo ""
        
        echo "## Recommendations"
        echo "1. Monitor response times and adjust worker processes as needed"
        echo "2. Scale horizontally when CPU usage exceeds 70%"
        echo "3. Implement caching for frequently accessed data"
        echo "4. Consider database connection pooling optimization"
        echo "5. Monitor memory usage and adjust limits accordingly"
        
    } > "$report_file"
    
    log "Performance report generated: $report_file"
}

# Monitor performance continuously
monitor_performance() {
    local duration="${1:-300}"  # Default 5 minutes
    
    log "Starting performance monitoring for $duration seconds..."
    
    local end_time=$(($(date +%s) + duration))
    
    while [[ $(date +%s) -lt $end_time ]]; do
        echo "$(date): Monitoring performance..."
        
        # Check API response time
        local response_time=$(curl -o /dev/null -s -w "%{time_total}" "http://localhost:8000/health" 2>/dev/null || echo "N/A")
        echo "API Response Time: ${response_time}s"
        
        # Check resource usage
        kubectl top pods -n "$NAMESPACE" --no-headers 2>/dev/null | while read -r line; do
            echo "Resource Usage: $line"
        done
        
        echo "---"
        sleep 30
    done
    
    log "Performance monitoring completed"
}

# Stress test the system
stress_test() {
    log "Running stress test..."
    
    # Create high-load test
    cat > "$PERFORMANCE_DIR/stress-test.py" << 'EOF'
import asyncio
import aiohttp
import time
import json

async def stress_request(session, url, payload):
    try:
        async with session.post(url, json=payload) as response:
            return await response.json()
    except Exception as e:
        return {"error": str(e)}

async def run_stress_test():
    url = "http://localhost:8000/api/chat"
    payload = {
        "query": "What are the legal requirements for data protection?",
        "session_id": "stress_test"
    }
    
    async with aiohttp.ClientSession() as session:
        tasks = []
        start_time = time.time()
        
        # Create 100 concurrent requests
        for i in range(100):
            task = stress_request(session, url, payload)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        success_count = sum(1 for r in results if "error" not in r)
        total_time = end_time - start_time
        
        print(f"Stress Test Results:")
        print(f"Total Requests: {len(results)}")
        print(f"Successful Requests: {success_count}")
        print(f"Failed Requests: {len(results) - success_count}")
        print(f"Total Time: {total_time:.2f}s")
        print(f"Requests/Second: {len(results)/total_time:.2f}")

if __name__ == "__main__":
    asyncio.run(run_stress_test())
EOF

    # Run stress test
    python3 "$PERFORMANCE_DIR/stress-test.py"
    
    log "Stress test completed"
}

# Main optimization function
optimize_performance() {
    log "Starting performance optimization..."
    
    check_prerequisites
    install_performance_tools
    apply_performance_configs
    optimize_databases
    run_benchmarks
    analyze_performance
    generate_performance_report
    
    log "Performance optimization completed successfully!"
    
    info "Next steps:"
    echo "1. Review the performance report"
    echo "2. Monitor system performance continuously"
    echo "3. Adjust resource limits based on actual usage"
    echo "4. Implement additional caching if needed"
    echo "5. Consider horizontal scaling for high load"
}

# Script usage
usage() {
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  optimize     - Run full performance optimization"
    echo "  benchmark    - Run performance benchmarks"
    echo "  analyze      - Analyze current performance"
    echo "  monitor [s]  - Monitor performance (default: 300s)"
    echo "  stress       - Run stress test"
    echo "  report       - Generate performance report"
    echo ""
    echo "Examples:"
    echo "  $0 optimize"
    echo "  $0 monitor 600"
    echo "  $0 stress"
}

# Main script logic
case "${1:-optimize}" in
    optimize)
        optimize_performance
        ;;
    benchmark)
        run_benchmarks
        ;;
    analyze)
        analyze_performance
        ;;
    monitor)
        monitor_performance "$2"
        ;;
    stress)
        stress_test
        ;;
    report)
        generate_performance_report
        ;;
    *)
        usage
        exit 1
        ;;
esac
