#!/bin/bash
# Health Check Script for RAG Legal Chatbot Production Environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
HEALTH_CHECK_TIMEOUT=30
API_BASE_URL="http://localhost:8000"
COMPOSE_FILE="$PROJECT_ROOT/docker-compose.prod.yml"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✓ $1${NC}"
}

fail() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ✗ $1${NC}"
}

# Check if Docker is running
check_docker() {
    info "Checking Docker daemon..."
    
    if ! docker info &> /dev/null; then
        fail "Docker daemon is not running"
        return 1
    fi
    
    success "Docker daemon is running"
    return 0
}

# Check Docker Compose services
check_compose_services() {
    info "Checking Docker Compose services..."
    
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        fail "Docker Compose file not found: $COMPOSE_FILE"
        return 1
    fi
    
    cd "$PROJECT_ROOT"
    
    # Get service status
    local services=$(docker-compose -f "$COMPOSE_FILE" ps --services)
    local failed_services=()
    
    for service in $services; do
        local status=$(docker-compose -f "$COMPOSE_FILE" ps -q "$service" | xargs docker inspect --format='{{.State.Status}}' 2>/dev/null || echo "not_found")
        
        case "$status" in
            "running")
                success "Service $service is running"
                ;;
            "exited")
                fail "Service $service has exited"
                failed_services+=("$service")
                ;;
            "not_found")
                fail "Service $service not found"
                failed_services+=("$service")
                ;;
            *)
                warn "Service $service status: $status"
                ;;
        esac
    done
    
    if [[ ${#failed_services[@]} -gt 0 ]]; then
        error "Failed services: ${failed_services[*]}"
        return 1
    fi
    
    success "All Docker Compose services are healthy"
    return 0
}

# Check individual container health
check_container_health() {
    info "Checking container health..."
    
    cd "$PROJECT_ROOT"
    
    # Get all containers from compose
    local containers=$(docker-compose -f "$COMPOSE_FILE" ps -q)
    local unhealthy_containers=()
    
    for container in $containers; do
        local container_name=$(docker inspect --format='{{.Name}}' "$container" | sed 's/^\/*//')
        local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no_healthcheck")
        
        case "$health_status" in
            "healthy")
                success "Container $container_name is healthy"
                ;;
            "unhealthy")
                fail "Container $container_name is unhealthy"
                unhealthy_containers+=("$container_name")
                ;;
            "starting")
                warn "Container $container_name is starting"
                ;;
            "no_healthcheck")
                info "Container $container_name has no health check"
                ;;
            *)
                warn "Container $container_name health status: $health_status"
                ;;
        esac
    done
    
    if [[ ${#unhealthy_containers[@]} -gt 0 ]]; then
        error "Unhealthy containers: ${unhealthy_containers[*]}"
        return 1
    fi
    
    success "All containers with health checks are healthy"
    return 0
}

# Check API endpoints
check_api_endpoints() {
    info "Checking API endpoints..."
    
    local endpoints=(
        "/health:GET:200"
        "/api/health:GET:200"
        "/metrics:GET:200"
    )
    
    local failed_endpoints=()
    
    for endpoint_config in "${endpoints[@]}"; do
        IFS=':' read -r path method expected_status <<< "$endpoint_config"
        local url="$API_BASE_URL$path"
        
        local response=$(curl -s -w "%{http_code}" -X "$method" "$url" -o /dev/null --max-time "$HEALTH_CHECK_TIMEOUT" 2>/dev/null || echo "000")
        
        if [[ "$response" == "$expected_status" ]]; then
            success "Endpoint $method $path returned $response"
        else
            fail "Endpoint $method $path returned $response (expected $expected_status)"
            failed_endpoints+=("$path")
        fi
    done
    
    if [[ ${#failed_endpoints[@]} -gt 0 ]]; then
        error "Failed endpoints: ${failed_endpoints[*]}"
        return 1
    fi
    
    success "All API endpoints are responding correctly"
    return 0
}

# Check database connections
check_databases() {
    info "Checking database connections..."
    
    local failed_dbs=()
    
    # Check Redis
    if docker exec rag-redis-prod redis-cli ping &> /dev/null; then
        success "Redis is responding"
    else
        fail "Redis is not responding"
        failed_dbs+=("Redis")
    fi
    
    # Check Milvus
    if curl -s "http://localhost:9091/healthz" --max-time "$HEALTH_CHECK_TIMEOUT" | grep -q "OK"; then
        success "Milvus is responding"
    else
        fail "Milvus is not responding"
        failed_dbs+=("Milvus")
    fi
    
    if [[ ${#failed_dbs[@]} -gt 0 ]]; then
        error "Failed databases: ${failed_dbs[*]}"
        return 1
    fi
    
    success "All databases are responding"
    return 0
}

# Check resource usage
check_resource_usage() {
    info "Checking resource usage..."
    
    cd "$PROJECT_ROOT"
    
    # Get container resource usage
    local containers=$(docker-compose -f "$COMPOSE_FILE" ps -q)
    local high_usage_containers=()
    
    for container in $containers; do
        local container_name=$(docker inspect --format='{{.Name}}' "$container" | sed 's/^\/*//')
        local stats=$(docker stats --no-stream --format "table {{.CPUPerc}}\t{{.MemUsage}}" "$container" 2>/dev/null | tail -n 1)
        
        if [[ -n "$stats" ]]; then
            local cpu_percent=$(echo "$stats" | awk '{print $1}' | sed 's/%//')
            local mem_usage=$(echo "$stats" | awk '{print $2}')
            
            # Check if CPU usage is above 90%
            if (( $(echo "$cpu_percent > 90" | bc -l) )); then
                warn "Container $container_name CPU usage: ${cpu_percent}%"
                high_usage_containers+=("$container_name")
            else
                success "Container $container_name CPU usage: ${cpu_percent}%"
            fi
            
            info "Container $container_name memory usage: $mem_usage"
        fi
    done
    
    # Check disk usage
    local disk_usage=$(df -h "$PROJECT_ROOT" | tail -n 1 | awk '{print $5}' | sed 's/%//')
    if (( disk_usage > 90 )); then
        warn "Disk usage is high: ${disk_usage}%"
    else
        success "Disk usage: ${disk_usage}%"
    fi
    
    if [[ ${#high_usage_containers[@]} -gt 0 ]]; then
        warn "High CPU usage containers: ${high_usage_containers[*]}"
    fi
    
    success "Resource usage check completed"
    return 0
}

# Check logs for errors
check_logs() {
    info "Checking recent logs for errors..."
    
    cd "$PROJECT_ROOT"
    
    local services=("api" "worker")
    local error_count=0
    
    for service in "${services[@]}"; do
        local recent_errors=$(docker-compose -f "$COMPOSE_FILE" logs --tail=100 "$service" 2>/dev/null | grep -i "error\|exception\|failed\|critical" | wc -l)
        
        if [[ "$recent_errors" -gt 0 ]]; then
            warn "Service $service has $recent_errors recent error(s)"
            error_count=$((error_count + recent_errors))
        else
            success "Service $service has no recent errors"
        fi
    done
    
    if [[ "$error_count" -gt 10 ]]; then
        warn "High number of recent errors: $error_count"
    else
        success "Error count is acceptable: $error_count"
    fi
    
    return 0
}

# Check external dependencies
check_external_dependencies() {
    info "Checking external dependencies..."
    
    local failed_deps=()
    
    # Check Google AI API (if API key is available)
    if curl -s "https://generativelanguage.googleapis.com/v1beta/models" --max-time "$HEALTH_CHECK_TIMEOUT" &> /dev/null; then
        success "Google AI API is reachable"
    else
        warn "Google AI API is not reachable (may be due to missing API key)"
    fi
    
    # Check Cohere API
    if curl -s "https://api.cohere.ai/v1/models" --max-time "$HEALTH_CHECK_TIMEOUT" &> /dev/null; then
        success "Cohere API is reachable"
    else
        warn "Cohere API is not reachable"
    fi
    
    # Check DNS resolution
    if nslookup google.com &> /dev/null; then
        success "DNS resolution is working"
    else
        fail "DNS resolution is not working"
        failed_deps+=("DNS")
    fi
    
    if [[ ${#failed_deps[@]} -gt 0 ]]; then
        error "Failed external dependencies: ${failed_deps[*]}"
        return 1
    fi
    
    success "External dependencies check completed"
    return 0
}

# Generate health report
generate_health_report() {
    local report_file="$PROJECT_ROOT/health-report-$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "RAG Legal Chatbot Health Report"
        echo "Generated: $(date)"
        echo "========================================"
        echo ""
        
        echo "System Information:"
        echo "- OS: $(uname -s)"
        echo "- Kernel: $(uname -r)"
        echo "- Architecture: $(uname -m)"
        echo "- Uptime: $(uptime)"
        echo ""
        
        echo "Docker Information:"
        docker version --format "- Docker Version: {{.Server.Version}}"
        docker-compose version --short | sed 's/^/- /'
        echo ""
        
        echo "Service Status:"
        cd "$PROJECT_ROOT"
        docker-compose -f "$COMPOSE_FILE" ps
        echo ""
        
        echo "Container Resource Usage:"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
        echo ""
        
        echo "Recent Logs (Last 50 lines):"
        docker-compose -f "$COMPOSE_FILE" logs --tail=50
        
    } > "$report_file"
    
    log "Health report generated: $report_file"
}

# Comprehensive health check
comprehensive_check() {
    log "Starting comprehensive health check..."
    
    local checks=(
        "check_docker"
        "check_compose_services"
        "check_container_health"
        "check_api_endpoints"
        "check_databases"
        "check_resource_usage"
        "check_logs"
        "check_external_dependencies"
    )
    
    local failed_checks=()
    local total_checks=${#checks[@]}
    local passed_checks=0
    
    for check in "${checks[@]}"; do
        echo ""
        if $check; then
            passed_checks=$((passed_checks + 1))
        else
            failed_checks+=("$check")
        fi
    done
    
    echo ""
    echo "========================================"
    echo "Health Check Summary"
    echo "========================================"
    echo "Total checks: $total_checks"
    echo "Passed: $passed_checks"
    echo "Failed: ${#failed_checks[@]}"
    
    if [[ ${#failed_checks[@]} -eq 0 ]]; then
        success "All health checks passed! System is healthy."
        return 0
    else
        error "Failed checks: ${failed_checks[*]}"
        warn "System may have issues that need attention."
        return 1
    fi
}

# Quick health check
quick_check() {
    log "Starting quick health check..."
    
    local quick_checks=(
        "check_docker"
        "check_compose_services"
        "check_api_endpoints"
    )
    
    for check in "${quick_checks[@]}"; do
        if ! $check; then
            error "Quick health check failed at: $check"
            return 1
        fi
    done
    
    success "Quick health check passed!"
    return 0
}

# Monitor mode (continuous checking)
monitor_mode() {
    local interval="${1:-60}"
    
    log "Starting health monitoring (interval: ${interval}s)"
    log "Press Ctrl+C to stop monitoring"
    
    while true; do
        echo ""
        echo "========================================"
        echo "Health Check - $(date)"
        echo "========================================"
        
        if quick_check; then
            success "System is healthy"
        else
            error "System has issues"
        fi
        
        sleep "$interval"
    done
}

# Script usage
usage() {
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  comprehensive  - Run all health checks"
    echo "  quick         - Run quick health checks"
    echo "  docker        - Check Docker daemon"
    echo "  services      - Check Docker Compose services"
    echo "  containers    - Check container health"
    echo "  api           - Check API endpoints"
    echo "  databases     - Check database connections"
    echo "  resources     - Check resource usage"
    echo "  logs          - Check logs for errors"
    echo "  external      - Check external dependencies"
    echo "  report        - Generate health report"
    echo "  monitor [interval] - Continuous monitoring"
    echo ""
    echo "Examples:"
    echo "  $0 comprehensive"
    echo "  $0 quick"
    echo "  $0 monitor 30"
}

# Main script logic
case "${1:-comprehensive}" in
    comprehensive)
        comprehensive_check
        ;;
    quick)
        quick_check
        ;;
    docker)
        check_docker
        ;;
    services)
        check_compose_services
        ;;
    containers)
        check_container_health
        ;;
    api)
        check_api_endpoints
        ;;
    databases)
        check_databases
        ;;
    resources)
        check_resource_usage
        ;;
    logs)
        check_logs
        ;;
    external)
        check_external_dependencies
        ;;
    report)
        generate_health_report
        ;;
    monitor)
        monitor_mode "$2"
        ;;
    *)
        usage
        exit 1
        ;;
esac
