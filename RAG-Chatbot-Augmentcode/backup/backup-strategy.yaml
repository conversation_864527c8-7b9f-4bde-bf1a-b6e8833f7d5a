# Comprehensive Backup Strategy for RAG Legal Chatbot

# Velero Backup Configuration for Kubernetes Resources
apiVersion: velero.io/v1
kind: BackupStorageLocation
metadata:
  name: default
  namespace: velero
spec:
  provider: aws
  objectStorage:
    bucket: rag-chatbot-backups
    prefix: velero
  config:
    region: us-west-2
    s3ForcePathStyle: "false"
---
# Volume Snapshot Location for Persistent Volumes
apiVersion: velero.io/v1
kind: VolumeSnapshotLocation
metadata:
  name: default
  namespace: velero
spec:
  provider: aws
  config:
    region: us-west-2
---
# Daily Backup Schedule for Application Data
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: daily-backup
  namespace: velero
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  template:
    includedNamespaces:
    - rag-chatbot
    excludedResources:
    - events
    - events.events.k8s.io
    storageLocation: default
    volumeSnapshotLocations:
    - default
    ttl: 720h  # 30 days retention
    hooks:
      resources:
      - name: redis-backup-hook
        includedNamespaces:
        - rag-chatbot
        labelSelector:
          matchLabels:
            app: rag-legal-chatbot
            component: redis
        pre:
        - exec:
            container: redis
            command:
            - /bin/sh
            - -c
            - redis-cli BGSAVE
            timeout: 3m
        post:
        - exec:
            container: redis
            command:
            - /bin/sh
            - -c
            - redis-cli LASTSAVE
---
# Weekly Full Backup Schedule
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: weekly-full-backup
  namespace: velero
spec:
  schedule: "0 1 * * 0"  # Weekly on Sunday at 1 AM
  template:
    includedNamespaces:
    - rag-chatbot
    - monitoring
    includeClusterResources: true
    storageLocation: default
    volumeSnapshotLocations:
    - default
    ttl: 2160h  # 90 days retention
---
# Database Backup CronJob for Milvus
apiVersion: batch/v1
kind: CronJob
metadata:
  name: milvus-backup
  namespace: rag-chatbot
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: backup-service-account
          containers:
          - name: milvus-backup
            image: milvusdb/milvus:v2.3.0
            command:
            - /bin/sh
            - -c
            - |
              # Create backup directory
              BACKUP_DIR="/backup/milvus/$(date +%Y%m%d_%H%M%S)"
              mkdir -p "$BACKUP_DIR"
              
              # Export Milvus collections
              python3 << 'EOF'
              from pymilvus import connections, utility, Collection
              import json
              import os
              
              # Connect to Milvus
              connections.connect("default", host="milvus-service", port="19530")
              
              # List all collections
              collections = utility.list_collections()
              backup_info = {"collections": [], "timestamp": "$(date -Iseconds)"}
              
              for collection_name in collections:
                  collection = Collection(collection_name)
                  collection.load()
                  
                  # Get collection info
                  info = {
                      "name": collection_name,
                      "schema": collection.schema.to_dict(),
                      "num_entities": collection.num_entities,
                      "indexes": [index.to_dict() for index in collection.indexes]
                  }
                  backup_info["collections"].append(info)
                  
                  # Export data (for small collections)
                  if collection.num_entities < 100000:
                      entities = collection.query(expr="", output_fields=["*"])
                      with open(f"$BACKUP_DIR/{collection_name}_data.json", "w") as f:
                          json.dump(entities, f)
              
              # Save backup metadata
              with open("$BACKUP_DIR/backup_info.json", "w") as f:
                  json.dump(backup_info, f, indent=2)
              
              print(f"Backup completed: $BACKUP_DIR")
              EOF
              
              # Compress backup
              tar -czf "$BACKUP_DIR.tar.gz" -C "/backup/milvus" "$(basename $BACKUP_DIR)"
              rm -rf "$BACKUP_DIR"
              
              # Upload to S3 (if configured)
              if [ -n "$AWS_S3_BUCKET" ]; then
                  aws s3 cp "$BACKUP_DIR.tar.gz" "s3://$AWS_S3_BUCKET/milvus-backups/"
              fi
              
              # Cleanup old backups (keep 30 days)
              find /backup/milvus -name "*.tar.gz" -mtime +30 -delete
            env:
            - name: AWS_S3_BUCKET
              value: "rag-chatbot-backups"
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-storage-pvc
          restartPolicy: OnFailure
---
# Redis Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: redis-backup
  namespace: rag-chatbot
spec:
  schedule: "0 4 * * *"  # Daily at 4 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: backup-service-account
          containers:
          - name: redis-backup
            image: redis:7-alpine
            command:
            - /bin/sh
            - -c
            - |
              # Create backup directory
              BACKUP_DIR="/backup/redis/$(date +%Y%m%d_%H%M%S)"
              mkdir -p "$BACKUP_DIR"
              
              # Trigger Redis save
              redis-cli -h redis-service -p 6379 BGSAVE
              
              # Wait for save to complete
              while [ "$(redis-cli -h redis-service -p 6379 LASTSAVE)" = "$(redis-cli -h redis-service -p 6379 LASTSAVE)" ]; do
                  sleep 5
              done
              
              # Copy RDB file
              kubectl cp rag-chatbot/$(kubectl get pods -n rag-chatbot -l component=redis -o jsonpath='{.items[0].metadata.name}'):/data/dump.rdb "$BACKUP_DIR/dump.rdb"
              
              # Create backup metadata
              cat > "$BACKUP_DIR/backup_info.json" << EOF
              {
                "timestamp": "$(date -Iseconds)",
                "redis_version": "$(redis-cli -h redis-service -p 6379 INFO server | grep redis_version | cut -d: -f2 | tr -d '\r')",
                "db_size": "$(redis-cli -h redis-service -p 6379 DBSIZE)",
                "memory_usage": "$(redis-cli -h redis-service -p 6379 INFO memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')"
              }
              EOF
              
              # Compress backup
              tar -czf "$BACKUP_DIR.tar.gz" -C "/backup/redis" "$(basename $BACKUP_DIR)"
              rm -rf "$BACKUP_DIR"
              
              # Upload to S3
              if [ -n "$AWS_S3_BUCKET" ]; then
                  aws s3 cp "$BACKUP_DIR.tar.gz" "s3://$AWS_S3_BUCKET/redis-backups/"
              fi
              
              # Cleanup old backups
              find /backup/redis -name "*.tar.gz" -mtime +30 -delete
            env:
            - name: AWS_S3_BUCKET
              value: "rag-chatbot-backups"
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-storage-pvc
          restartPolicy: OnFailure
---
# Backup Storage PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backup-storage-pvc
  namespace: rag-chatbot
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd
---
# Backup Service Account
apiVersion: v1
kind: ServiceAccount
metadata:
  name: backup-service-account
  namespace: rag-chatbot
---
# Backup RBAC
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: rag-chatbot
  name: backup-role
rules:
- apiGroups: [""]
  resources: ["pods", "pods/exec"]
  verbs: ["get", "list", "create"]
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: backup-rolebinding
  namespace: rag-chatbot
subjects:
- kind: ServiceAccount
  name: backup-service-account
  namespace: rag-chatbot
roleRef:
  kind: Role
  name: backup-role
  apiGroup: rbac.authorization.k8s.io
