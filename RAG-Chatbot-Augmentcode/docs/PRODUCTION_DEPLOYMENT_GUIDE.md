# Production Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the RAG Legal Chatbot system in production environments. The system is designed for enterprise-grade deployment with high availability, security, and scalability.

## Prerequisites

### Infrastructure Requirements

- **Kubernetes Cluster**: v1.20+ with at least 16GB RAM and 8 CPU cores
- **Storage**: 500GB+ with fast SSD storage classes
- **Network**: Load balancer with SSL/TLS termination
- **DNS**: Domain name with SSL certificate management

### Required Tools

```bash
# Install required tools
kubectl version --client  # v1.20+
helm version              # v3.0+
docker version           # 20.10+
aws --version            # 2.0+ (if using AWS)
```

### Access Requirements

- Kubernetes cluster admin access
- Container registry access (GitHub Container Registry)
- DNS management access
- SSL certificate management access

## Quick Start

### 1. Clone Repository

```bash
git clone https://github.com/your-org/rag-chatbot.git
cd rag-chatbot
```

### 2. Configure Environment

```bash
# Copy production environment template
cp .env.production .env

# Edit with your actual values
nano .env
```

### 3. Deploy to Kubernetes

```bash
# Setup production environment
./scripts/setup-production.sh

# Deploy to Kubernetes
./k8s/deploy.sh deploy
```

### 4. Verify Deployment

```bash
# Check deployment status
./scripts/health-check.sh comprehensive

# Access the application
kubectl get ingress -n rag-chatbot
```

## Detailed Deployment Steps

### Step 1: Environment Preparation

#### 1.1 Create Namespace

```bash
kubectl create namespace rag-chatbot
kubectl label namespace rag-chatbot pod-security.kubernetes.io/enforce=restricted
```

#### 1.2 Setup Secrets

```bash
# Generate secure credentials
./scripts/secrets-manager.sh init

# Set API keys
./scripts/secrets-manager.sh set GOOGLE_API_KEY "your-google-api-key"
./scripts/secrets-manager.sh set COHERE_API_KEY "your-cohere-api-key"

# Export to Kubernetes
./scripts/secrets-manager.sh export
```

#### 1.3 Configure Storage

```bash
# Apply persistent volume configurations
kubectl apply -f k8s/persistent-volumes.yaml

# Verify storage classes
kubectl get storageclass
```

### Step 2: Database Deployment

#### 2.1 Deploy Redis

```bash
kubectl apply -f k8s/redis-deployment.yaml

# Wait for Redis to be ready
kubectl wait --for=condition=available deployment/redis -n rag-chatbot --timeout=300s
```

#### 2.2 Deploy Milvus Stack

```bash
# Deploy etcd, MinIO, and Milvus
kubectl apply -f k8s/milvus-deployment.yaml

# Wait for all components
kubectl wait --for=condition=available deployment/etcd -n rag-chatbot --timeout=300s
kubectl wait --for=condition=available deployment/minio -n rag-chatbot --timeout=300s
kubectl wait --for=condition=available deployment/milvus-standalone -n rag-chatbot --timeout=600s
```

### Step 3: Application Deployment

#### 3.1 Deploy API Service

```bash
kubectl apply -f k8s/api-deployment.yaml

# Verify deployment
kubectl get pods -n rag-chatbot -l component=api
kubectl logs -f deployment/rag-chatbot-api -n rag-chatbot
```

#### 3.2 Deploy Worker Service

```bash
kubectl apply -f k8s/worker-deployment.yaml

# Verify workers
kubectl get pods -n rag-chatbot -l component=worker
```

### Step 4: Load Balancer and Ingress

#### 4.1 Deploy Nginx

```bash
kubectl apply -f k8s/nginx-deployment.yaml

# Get external IP
kubectl get service nginx-service -n rag-chatbot
```

#### 4.2 Configure DNS

```bash
# Update DNS records to point to external IP
# Example: rag-chatbot.yourdomain.com -> EXTERNAL_IP
```

### Step 5: SSL/TLS Configuration

#### 5.1 Install cert-manager (if not present)

```bash
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml
```

#### 5.2 Configure Certificates

```bash
kubectl apply -f security/tls-certificates.yaml

# Verify certificate issuance
kubectl get certificates -n rag-chatbot
kubectl describe certificate rag-chatbot-tls -n rag-chatbot
```

## Configuration

### Environment Variables

Key configuration parameters in `.env`:

```bash
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Database Configuration
MILVUS_HOST=milvus-service
MILVUS_PORT=19530
REDIS_HOST=redis-service
REDIS_PORT=6379

# Security
SECRET_KEY=your-secret-key
ADMIN_API_KEY=your-admin-key

# External APIs
GOOGLE_API_KEY=your-google-api-key
COHERE_API_KEY=your-cohere-api-key
```

### Resource Limits

Default resource allocations:

| Component | CPU Request | Memory Request | CPU Limit | Memory Limit |
|-----------|-------------|----------------|-----------|--------------|
| API       | 500m        | 1Gi            | 1         | 2Gi          |
| Worker    | 500m        | 1Gi            | 1         | 2Gi          |
| Milvus    | 1           | 2Gi            | 2         | 4Gi          |
| Redis     | 250m        | 512Mi          | 500m      | 1Gi          |

### Auto-scaling Configuration

```yaml
# HPA for API
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: rag-chatbot-api-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: rag-chatbot-api
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

## Monitoring Setup

### Deploy Monitoring Stack

```bash
# Setup monitoring
./scripts/setup-monitoring.sh

# Access Grafana
kubectl port-forward service/grafana 3000:3000 -n monitoring
# Visit http://localhost:3000 (admin/admin)
```

### Key Metrics to Monitor

- **Response Time**: 95th percentile < 2 seconds
- **Error Rate**: < 1% of requests
- **CPU Usage**: < 70% average
- **Memory Usage**: < 80% of limits
- **Database Connections**: Monitor connection pool usage

## Security Hardening

### Apply Security Policies

```bash
# Apply comprehensive security hardening
./scripts/security-hardening.sh harden

# Verify security configuration
./scripts/security-hardening.sh validate
```

### Security Checklist

- [x] Pod Security Standards enforced
- [x] Network policies applied
- [x] RBAC configured with least privilege
- [x] Secrets encrypted and rotated
- [x] TLS/SSL certificates configured
- [x] Container images scanned for vulnerabilities
- [x] Security monitoring enabled

## Backup and Recovery

### Setup Backup System

```bash
# Setup backup infrastructure
./scripts/backup-restore.sh setup

# Create manual backup
./scripts/backup-restore.sh backup production-backup-$(date +%Y%m%d)

# Test disaster recovery
./scripts/backup-restore.sh test-dr
```

### Backup Schedule

- **Daily**: Application data and configurations
- **Weekly**: Full system backup including volumes
- **Monthly**: Long-term archival backup

## Performance Optimization

### Apply Performance Configurations

```bash
# Run performance optimization
./scripts/performance-optimization.sh optimize

# Monitor performance
./scripts/performance-optimization.sh monitor 600
```

### Performance Targets

- **Response Time**: < 2s (95th percentile)
- **Throughput**: > 100 requests/second
- **Availability**: 99.9% uptime
- **Scalability**: Auto-scale from 2-10 replicas

## Troubleshooting

### Common Issues

#### 1. Pods Not Starting

```bash
# Check pod status
kubectl get pods -n rag-chatbot

# Check events
kubectl get events -n rag-chatbot --sort-by='.lastTimestamp'

# Check logs
kubectl logs -f deployment/rag-chatbot-api -n rag-chatbot
```

#### 2. Database Connection Issues

```bash
# Test Redis connection
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli ping

# Test Milvus connection
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- curl http://localhost:9091/healthz
```

#### 3. SSL Certificate Issues

```bash
# Check certificate status
kubectl get certificates -n rag-chatbot
kubectl describe certificate rag-chatbot-tls -n rag-chatbot

# Check cert-manager logs
kubectl logs -f deployment/cert-manager -n cert-manager
```

### Health Checks

```bash
# Comprehensive health check
./scripts/health-check.sh comprehensive

# Quick health check
./scripts/health-check.sh quick

# Generate health report
./scripts/health-check.sh report
```

## Maintenance

### Regular Maintenance Tasks

#### Daily
- Monitor system health and alerts
- Check backup completion
- Review error logs

#### Weekly
- Update dependencies (automated via Dependabot)
- Review security scan results
- Performance analysis

#### Monthly
- Security audit
- Capacity planning review
- Disaster recovery testing

### Update Procedures

#### Application Updates

```bash
# Update to new version
kubectl set image deployment/rag-chatbot-api api=ghcr.io/your-org/rag-chatbot-api:v2.0.0 -n rag-chatbot

# Monitor rollout
kubectl rollout status deployment/rag-chatbot-api -n rag-chatbot

# Rollback if needed
kubectl rollout undo deployment/rag-chatbot-api -n rag-chatbot
```

#### Infrastructure Updates

```bash
# Update Kubernetes manifests
git pull origin main
kubectl apply -f k8s/

# Verify updates
./scripts/health-check.sh comprehensive
```

## Support and Escalation

### Contact Information

- **On-call Engineer**: +1-XXX-XXX-XXXX
- **DevOps Team**: <EMAIL>
- **Security Team**: <EMAIL>

### Escalation Procedures

1. **Level 1**: Application issues, performance degradation
2. **Level 2**: Infrastructure issues, security incidents
3. **Level 3**: Critical system failures, data loss

### Emergency Procedures

#### System Down

1. Check system status: `./scripts/health-check.sh quick`
2. Review recent changes and logs
3. Initiate rollback if recent deployment
4. Escalate to on-call engineer
5. Activate disaster recovery if needed

#### Data Loss

1. Stop all write operations
2. Assess scope of data loss
3. Initiate backup restoration
4. Notify stakeholders
5. Conduct post-incident review

## Compliance and Auditing

### Audit Logging

- All API requests logged
- Database access logged
- Administrative actions logged
- Security events logged

### Compliance Requirements

- **GDPR**: Data protection and privacy
- **SOC 2**: Security and availability controls
- **ISO 27001**: Information security management

### Regular Audits

- **Security**: Quarterly security assessments
- **Performance**: Monthly performance reviews
- **Compliance**: Annual compliance audits

---

For additional support, refer to:
- [Monitoring Guide](./MONITORING_GUIDE.md)
- [Security Guide](./SECURITY_GUIDE.md)
- [Troubleshooting Guide](./TROUBLESHOOTING_GUIDE.md)
- [API Documentation](./API_DOCUMENTATION.md)
