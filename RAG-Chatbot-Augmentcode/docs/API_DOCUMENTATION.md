# RAG Legal Chatbot API Documentation

## Overview

The RAG Legal Chatbot API provides endpoints for legal document querying, conversation management, and administrative functions. The API is built with FastAPI and follows REST principles with OpenAPI/Swagger documentation.

## Base URL

- **Production**: `https://rag-chatbot.yourdomain.com/api`
- **Staging**: `https://staging-rag-chatbot.yourdomain.com/api`
- **Development**: `http://localhost:8000/api`

## Authentication

### API Key Authentication

```http
Authorization: Bearer YOUR_API_KEY
```

### Admin Authentication

```http
Authorization: Bearer YOUR_ADMIN_API_KEY
```

## Core Endpoints

### Chat Endpoints

#### POST /chat

Submit a legal query and receive an AI-generated response with sources.

**Request:**
```json
{
  "query": "What are the requirements for forming a limited liability company in Germany?",
  "session_id": "user_session_123",
  "max_results": 5,
  "include_sources": true,
  "language": "de",
  "filters": {
    "document_type": "statute",
    "jurisdiction": "germany"
  }
}
```

**Response:**
```json
{
  "answer": "To form a limited liability company (GmbH) in Germany, you need to meet several requirements...",
  "sources": [
    {
      "document_id": "doc_123",
      "title": "German Limited Liability Company Act",
      "excerpt": "Relevant text excerpt...",
      "score": 0.95,
      "metadata": {
        "document_type": "statute",
        "jurisdiction": "germany",
        "last_updated": "2024-01-15"
      }
    }
  ],
  "session_id": "user_session_123",
  "query_id": "query_456",
  "response_time_ms": 1250,
  "confidence_score": 0.92
}
```

**Status Codes:**
- `200`: Success
- `400`: Invalid request parameters
- `429`: Rate limit exceeded
- `500`: Internal server error

#### POST /chat/stream

Stream a legal query response in real-time.

**Request:**
```json
{
  "query": "Explain the process of trademark registration",
  "session_id": "user_session_123",
  "stream": true
}
```

**Response (Server-Sent Events):**
```
data: {"type": "start", "query_id": "query_789"}

data: {"type": "chunk", "content": "Trademark registration involves"}

data: {"type": "chunk", "content": " several key steps..."}

data: {"type": "sources", "sources": [...]}

data: {"type": "end", "query_id": "query_789"}
```

### Search Endpoints

#### POST /search

Search legal documents using vector similarity and keyword matching.

**Request:**
```json
{
  "query": "intellectual property protection",
  "limit": 10,
  "offset": 0,
  "filters": {
    "document_type": ["statute", "regulation"],
    "jurisdiction": "germany",
    "date_range": {
      "start": "2020-01-01",
      "end": "2024-12-31"
    }
  },
  "search_type": "hybrid"
}
```

**Response:**
```json
{
  "results": [
    {
      "document_id": "doc_456",
      "title": "Intellectual Property Protection Act",
      "content": "Document content excerpt...",
      "score": 0.89,
      "metadata": {
        "document_type": "statute",
        "jurisdiction": "germany",
        "publication_date": "2023-06-15"
      }
    }
  ],
  "total_results": 25,
  "query_time_ms": 150,
  "search_metadata": {
    "vector_results": 8,
    "keyword_results": 17,
    "reranked": true
  }
}
```

### Conversation Management

#### GET /conversations/{session_id}

Retrieve conversation history for a session.

**Response:**
```json
{
  "session_id": "user_session_123",
  "conversations": [
    {
      "query_id": "query_123",
      "query": "What is a trademark?",
      "answer": "A trademark is a sign...",
      "timestamp": "2024-12-01T10:30:00Z",
      "sources_count": 3
    }
  ],
  "total_queries": 5,
  "session_created": "2024-12-01T10:00:00Z",
  "last_activity": "2024-12-01T11:15:00Z"
}
```

#### DELETE /conversations/{session_id}

Clear conversation history for a session.

**Response:**
```json
{
  "message": "Conversation history cleared",
  "session_id": "user_session_123",
  "queries_deleted": 5
}
```

### Document Management

#### POST /documents/upload

Upload legal documents for indexing (Admin only).

**Request (multipart/form-data):**
```
file: document.pdf
metadata: {
  "document_type": "statute",
  "jurisdiction": "germany",
  "title": "Data Protection Act",
  "tags": ["privacy", "gdpr"]
}
```

**Response:**
```json
{
  "document_id": "doc_789",
  "status": "uploaded",
  "processing_job_id": "job_123",
  "estimated_processing_time": "5 minutes"
}
```

#### GET /documents/{document_id}

Retrieve document metadata and content.

**Response:**
```json
{
  "document_id": "doc_789",
  "title": "Data Protection Act",
  "content": "Full document content...",
  "metadata": {
    "document_type": "statute",
    "jurisdiction": "germany",
    "upload_date": "2024-12-01T09:00:00Z",
    "file_size": 1024000,
    "page_count": 45
  },
  "processing_status": "completed",
  "indexed": true
}
```

## Administrative Endpoints

### System Health

#### GET /health

Basic health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-12-01T12:00:00Z",
  "version": "1.0.0"
}
```

#### GET /admin/health

Detailed system health check (Admin only).

**Response:**
```json
{
  "status": "healthy",
  "components": {
    "database": {
      "redis": "healthy",
      "milvus": "healthy"
    },
    "external_apis": {
      "google_ai": "healthy",
      "cohere": "healthy"
    },
    "workers": {
      "active": 3,
      "status": "healthy"
    }
  },
  "metrics": {
    "response_time_ms": 45,
    "memory_usage_mb": 1024,
    "cpu_usage_percent": 25
  }
}
```

### Statistics and Analytics

#### GET /admin/stats

System usage statistics (Admin only).

**Response:**
```json
{
  "period": "24h",
  "queries": {
    "total": 1250,
    "successful": 1200,
    "failed": 50,
    "average_response_time_ms": 1100
  },
  "users": {
    "active_sessions": 45,
    "unique_users": 120
  },
  "documents": {
    "total_indexed": 5000,
    "recently_added": 25
  },
  "performance": {
    "cache_hit_rate": 0.85,
    "average_cpu_usage": 0.35,
    "average_memory_usage": 0.60
  }
}
```

### Configuration Management

#### GET /admin/config

Retrieve system configuration (Admin only).

**Response:**
```json
{
  "api_settings": {
    "max_query_length": 1000,
    "default_max_results": 5,
    "rate_limit_per_minute": 60
  },
  "model_settings": {
    "embedding_model": "text-embedding-004",
    "llm_model": "gemini-1.5-flash",
    "temperature": 0.1
  },
  "search_settings": {
    "vector_search_k": 20,
    "reranking_enabled": true,
    "hybrid_search_alpha": 0.7
  }
}
```

#### PUT /admin/config

Update system configuration (Admin only).

**Request:**
```json
{
  "api_settings": {
    "rate_limit_per_minute": 100
  },
  "model_settings": {
    "temperature": 0.2
  }
}
```

## Error Handling

### Error Response Format

```json
{
  "error": {
    "code": "INVALID_QUERY",
    "message": "Query text is required",
    "details": {
      "field": "query",
      "constraint": "min_length_1"
    }
  },
  "request_id": "req_123456",
  "timestamp": "2024-12-01T12:00:00Z"
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `INVALID_QUERY` | 400 | Query validation failed |
| `SESSION_NOT_FOUND` | 404 | Session ID not found |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `UNAUTHORIZED` | 401 | Invalid or missing API key |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `INTERNAL_ERROR` | 500 | Server error |
| `SERVICE_UNAVAILABLE` | 503 | External service unavailable |

## Rate Limiting

### Default Limits

- **Standard Users**: 60 requests per minute
- **Premium Users**: 300 requests per minute
- **Admin Users**: 1000 requests per minute

### Rate Limit Headers

```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1701432000
```

## Webhooks

### Query Completion Webhook

Configure webhooks to receive notifications when queries are processed.

**Webhook Payload:**
```json
{
  "event": "query.completed",
  "query_id": "query_123",
  "session_id": "user_session_123",
  "query": "What is copyright law?",
  "response_time_ms": 1200,
  "success": true,
  "timestamp": "2024-12-01T12:00:00Z"
}
```

## SDKs and Libraries

### Python SDK

```python
from rag_chatbot_client import ChatbotClient

client = ChatbotClient(
    api_key="your_api_key",
    base_url="https://rag-chatbot.yourdomain.com/api"
)

response = client.chat(
    query="What are the requirements for a trademark?",
    session_id="my_session"
)

print(response.answer)
for source in response.sources:
    print(f"Source: {source.title}")
```

### JavaScript SDK

```javascript
import { ChatbotClient } from '@your-org/rag-chatbot-client';

const client = new ChatbotClient({
  apiKey: 'your_api_key',
  baseUrl: 'https://rag-chatbot.yourdomain.com/api'
});

const response = await client.chat({
  query: 'What are the requirements for a trademark?',
  sessionId: 'my_session'
});

console.log(response.answer);
```

## OpenAPI Specification

The complete OpenAPI specification is available at:
- **Interactive Docs**: `https://rag-chatbot.yourdomain.com/docs`
- **ReDoc**: `https://rag-chatbot.yourdomain.com/redoc`
- **OpenAPI JSON**: `https://rag-chatbot.yourdomain.com/openapi.json`

## Support

For API support and questions:
- **Documentation**: [https://docs.rag-chatbot.yourdomain.com](https://docs.rag-chatbot.yourdomain.com)
- **Support Email**: <EMAIL>
- **Status Page**: [https://status.rag-chatbot.yourdomain.com](https://status.rag-chatbot.yourdomain.com)

---

**API Version**: v1.0.0  
**Last Updated**: December 2024  
**Next Version**: v1.1.0 (Q1 2025)
