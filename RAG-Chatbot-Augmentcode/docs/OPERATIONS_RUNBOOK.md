# Operations Runbook

## Overview

This runbook provides step-by-step procedures for operating the RAG Legal Chatbot system in production. It covers routine operations, incident response, and emergency procedures.

## System Architecture

```
Internet → Load Balancer → Nginx → API Service (2-10 replicas)
                                 → Worker Service (1-5 replicas)
                                 → Redis Cluster
                                 → Milvus Vector DB
                                 → Monitoring Stack
```

## Daily Operations

### Morning Health Check (9:00 AM)

```bash
# 1. Check overall system health
./scripts/health-check.sh quick

# 2. Review overnight alerts
kubectl get events -n rag-chatbot --sort-by='.lastTimestamp' | tail -20

# 3. Check resource usage
kubectl top pods -n rag-chatbot
kubectl top nodes

# 4. Verify backup completion
./scripts/backup-restore.sh list | head -5

# 5. Check application metrics
curl -s http://prometheus:9090/api/v1/query?query=up | jq '.data.result[] | select(.metric.job=="rag-chatbot-api")'
```

### Evening Review (6:00 PM)

```bash
# 1. Generate daily report
./scripts/health-check.sh report

# 2. Review error logs
kubectl logs --since=24h -l app=rag-legal-chatbot -n rag-chatbot | grep -i error

# 3. Check performance metrics
./scripts/performance-optimization.sh analyze

# 4. Verify auto-scaling events
kubectl get events -n rag-chatbot | grep -i "scaled\|scaling"
```

## Incident Response Procedures

### Severity Levels

- **P1 (Critical)**: System down, data loss, security breach
- **P2 (High)**: Significant performance degradation, partial outage
- **P3 (Medium)**: Minor issues, non-critical functionality affected
- **P4 (Low)**: Cosmetic issues, enhancement requests

### P1 - Critical Incident Response

#### System Down

**Symptoms**: Health checks failing, 5xx errors, no response

**Immediate Actions** (5 minutes):
```bash
# 1. Confirm outage
curl -f http://your-domain.com/health || echo "CONFIRMED: System is down"

# 2. Check pod status
kubectl get pods -n rag-chatbot

# 3. Check recent deployments
kubectl rollout history deployment/rag-chatbot-api -n rag-chatbot

# 4. Check resource availability
kubectl describe nodes | grep -A 5 "Allocated resources"
```

**Investigation** (10 minutes):
```bash
# 1. Check application logs
kubectl logs --tail=100 -l component=api -n rag-chatbot

# 2. Check database connectivity
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli ping
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- curl http://localhost:9091/healthz

# 3. Check ingress/load balancer
kubectl get ingress -n rag-chatbot
kubectl describe service nginx-service -n rag-chatbot
```

**Resolution Actions**:
```bash
# Option 1: Restart services
kubectl rollout restart deployment/rag-chatbot-api -n rag-chatbot
kubectl rollout restart deployment/rag-chatbot-worker -n rag-chatbot

# Option 2: Rollback recent deployment
kubectl rollout undo deployment/rag-chatbot-api -n rag-chatbot

# Option 3: Scale up resources
kubectl scale deployment/rag-chatbot-api --replicas=5 -n rag-chatbot

# Option 4: Disaster recovery
./scripts/backup-restore.sh restore latest-backup
```

#### Data Loss

**Immediate Actions**:
```bash
# 1. Stop all write operations
kubectl scale deployment/rag-chatbot-api --replicas=0 -n rag-chatbot
kubectl scale deployment/rag-chatbot-worker --replicas=0 -n rag-chatbot

# 2. Assess data integrity
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli DBSIZE
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- python3 -c "
from pymilvus import connections, utility
connections.connect('default', host='localhost', port='19530')
print('Collections:', utility.list_collections())
"

# 3. Initiate backup restoration
./scripts/backup-restore.sh restore latest-backup
```

### P2 - High Priority Response

#### Performance Degradation

**Symptoms**: Slow response times, high CPU/memory usage

**Investigation**:
```bash
# 1. Check response times
curl -w "@curl-format.txt" -o /dev/null -s http://your-domain.com/health

# 2. Check resource usage
kubectl top pods -n rag-chatbot
kubectl describe hpa -n rag-chatbot

# 3. Check database performance
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli --latency-history
```

**Resolution**:
```bash
# 1. Scale up if needed
kubectl scale deployment/rag-chatbot-api --replicas=8 -n rag-chatbot

# 2. Optimize database
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli CONFIG SET maxmemory-policy allkeys-lru

# 3. Clear cache if needed
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli FLUSHDB
```

## Maintenance Procedures

### Planned Maintenance Window

**Pre-maintenance** (30 minutes before):
```bash
# 1. Notify users (if applicable)
# 2. Create backup
./scripts/backup-restore.sh backup maintenance-$(date +%Y%m%d_%H%M%S)

# 3. Scale up for redundancy
kubectl scale deployment/rag-chatbot-api --replicas=4 -n rag-chatbot

# 4. Verify system health
./scripts/health-check.sh comprehensive
```

**During maintenance**:
```bash
# 1. Apply updates
kubectl apply -f k8s/

# 2. Rolling update
kubectl set image deployment/rag-chatbot-api api=new-image:tag -n rag-chatbot

# 3. Monitor rollout
kubectl rollout status deployment/rag-chatbot-api -n rag-chatbot --timeout=600s
```

**Post-maintenance**:
```bash
# 1. Verify functionality
./scripts/health-check.sh comprehensive

# 2. Run smoke tests
curl -X POST http://your-domain.com/api/chat \
  -H "Content-Type: application/json" \
  -d '{"query": "Test question", "session_id": "test"}'

# 3. Monitor for 30 minutes
./scripts/performance-optimization.sh monitor 1800
```

### Database Maintenance

#### Redis Maintenance

```bash
# 1. Check Redis health
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli INFO

# 2. Backup Redis
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli BGSAVE

# 3. Optimize memory
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli MEMORY PURGE

# 4. Check slow queries
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli SLOWLOG GET 10
```

#### Milvus Maintenance

```bash
# 1. Check Milvus status
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- curl http://localhost:9091/healthz

# 2. Compact collections
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- python3 -c "
from pymilvus import connections, Collection
connections.connect('default', host='localhost', port='19530')
collection = Collection('legal_docs_v1')
collection.compact()
"

# 3. Check collection statistics
kubectl exec -it deployment/milvus-standalone -n rag-chatbot -- python3 -c "
from pymilvus import connections, Collection
connections.connect('default', host='localhost', port='19530')
collection = Collection('legal_docs_v1')
print('Entities:', collection.num_entities)
print('Indexes:', collection.indexes)
"
```

## Monitoring and Alerting

### Key Metrics Dashboard

Access Grafana: `kubectl port-forward service/grafana 3000:3000 -n monitoring`

**Critical Metrics**:
- API Response Time (95th percentile)
- Error Rate (4xx/5xx responses)
- CPU/Memory Usage
- Database Connection Pool
- Queue Length (Celery)

### Alert Response

#### High Error Rate Alert

```bash
# 1. Check error logs
kubectl logs --since=10m -l component=api -n rag-chatbot | grep -i error

# 2. Check external dependencies
curl -f https://generativelanguage.googleapis.com/v1beta/models
curl -f https://api.cohere.ai/v1/models

# 3. Check database connectivity
kubectl exec -it deployment/redis -n rag-chatbot -- redis-cli ping
```

#### High Memory Usage Alert

```bash
# 1. Check memory usage
kubectl top pods -n rag-chatbot

# 2. Check for memory leaks
kubectl exec -it deployment/rag-chatbot-api -n rag-chatbot -- ps aux

# 3. Restart if necessary
kubectl rollout restart deployment/rag-chatbot-api -n rag-chatbot
```

## Security Procedures

### Security Incident Response

#### Suspected Breach

**Immediate Actions**:
```bash
# 1. Isolate affected systems
kubectl patch networkpolicy default-deny-all -n rag-chatbot -p '{"spec":{"policyTypes":["Ingress","Egress"]}}'

# 2. Collect evidence
kubectl logs --since=1h -l app=rag-legal-chatbot -n rag-chatbot > security-incident-logs.txt

# 3. Check for unauthorized access
kubectl get events -n rag-chatbot | grep -i "unauthorized\|forbidden\|failed"

# 4. Notify security team
# Send <NAME_EMAIL>
```

#### Vulnerability Patching

```bash
# 1. Scan for vulnerabilities
./scripts/security-hardening.sh validate

# 2. Update container images
docker pull ghcr.io/your-org/rag-chatbot-api:latest
docker pull ghcr.io/your-org/rag-chatbot-worker:latest

# 3. Deploy updates
kubectl set image deployment/rag-chatbot-api api=ghcr.io/your-org/rag-chatbot-api:latest -n rag-chatbot

# 4. Verify security posture
./scripts/security-hardening.sh validate
```

## Backup and Recovery Operations

### Regular Backup Verification

```bash
# 1. List recent backups
./scripts/backup-restore.sh list

# 2. Verify backup integrity
./scripts/backup-restore.sh verify latest-backup

# 3. Test restore procedure (monthly)
./scripts/backup-restore.sh test-dr
```

### Emergency Recovery

```bash
# 1. Assess damage
kubectl get pods -n rag-chatbot
kubectl get pvc -n rag-chatbot

# 2. Restore from backup
./scripts/backup-restore.sh restore production-backup-YYYYMMDD

# 3. Verify data integrity
# Run application-specific data validation

# 4. Resume operations
kubectl scale deployment/rag-chatbot-api --replicas=3 -n rag-chatbot
```

## Performance Optimization

### Performance Monitoring

```bash
# 1. Check current performance
./scripts/performance-optimization.sh analyze

# 2. Run load test
./scripts/performance-optimization.sh stress

# 3. Optimize based on results
./scripts/performance-optimization.sh optimize
```

### Scaling Decisions

**Scale Up Triggers**:
- CPU usage > 70% for 5 minutes
- Memory usage > 80% for 5 minutes
- Response time > 2 seconds (95th percentile)
- Queue length > 100 tasks

**Scale Down Triggers**:
- CPU usage < 30% for 15 minutes
- Memory usage < 50% for 15 minutes
- Response time < 1 second (95th percentile)
- Queue length < 10 tasks

## Contact Information

### Escalation Matrix

| Severity | Primary Contact | Secondary Contact | Response Time |
|----------|----------------|-------------------|---------------|
| P1       | On-call Engineer | DevOps Lead      | 15 minutes    |
| P2       | DevOps Team     | Engineering Lead  | 1 hour        |
| P3       | Engineering Team| Product Manager   | 4 hours       |
| P4       | Engineering Team| Product Manager   | 24 hours      |

### Contact Details

- **On-call Engineer**: +1-XXX-XXX-XXXX
- **DevOps Team**: <EMAIL>
- **Security Team**: <EMAIL>
- **Engineering Lead**: <EMAIL>

### External Vendors

- **Cloud Provider**: AWS Support (if applicable)
- **Monitoring**: Grafana Support
- **Security**: Security vendor contact

## Documentation Updates

This runbook should be updated:
- After each incident (lessons learned)
- When procedures change
- Quarterly review and validation
- When new team members join

**Last Updated**: December 2024  
**Next Review**: March 2025  
**Owner**: DevOps Team
