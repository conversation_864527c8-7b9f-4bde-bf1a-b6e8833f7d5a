#!/usr/bin/env python3
"""
Simple test script to verify infrastructure services are working
"""

import redis
import requests
from pymilvus import connections, utility
import sys
import os

def test_redis():
    """Test Redis connection"""
    print("🔄 Testing Redis connection...")
    try:
        r = redis.Redis(host='localhost', port=6380, db=0)
        r.ping()
        r.set('test_key', 'test_value')
        value = r.get('test_key')
        if value == b'test_value':
            print("✅ Redis: Connection successful and working!")
            return True
        else:
            print("❌ Redis: Connection successful but data test failed")
            return False
    except Exception as e:
        print(f"❌ Redis: Connection failed - {e}")
        return False

def test_milvus():
    """Test Milvus connection"""
    print("🔄 Testing Milvus connection...")
    try:
        # Connect to Milvus
        connections.connect("default", host="localhost", port="19531")
        
        # Check if connection is successful
        if connections.has_connection("default"):
            print("✅ Milvus: Connection successful!")
            
            # List collections (should be empty initially)
            collections = utility.list_collections()
            print(f"📋 Milvus: Available collections: {collections}")
            return True
        else:
            print("❌ Milvus: Connection failed")
            return False
    except Exception as e:
        print(f"❌ Milvus: Connection failed - {e}")
        return False

def test_minio():
    """Test MinIO connection"""
    print("🔄 Testing MinIO connection...")
    try:
        # Test MinIO health endpoint
        response = requests.get('http://localhost:9002/minio/health/live', timeout=5)
        if response.status_code == 200:
            print("✅ MinIO: Health check successful!")
            return True
        else:
            print(f"❌ MinIO: Health check failed with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ MinIO: Connection failed - {e}")
        return False

def test_milvus_health():
    """Test Milvus health endpoint"""
    print("🔄 Testing Milvus health endpoint...")
    try:
        response = requests.get('http://localhost:9092/healthz', timeout=5)
        if response.status_code == 200:
            print("✅ Milvus Health: Endpoint responding!")
            return True
        else:
            print(f"❌ Milvus Health: Failed with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Milvus Health: Connection failed - {e}")
        return False

def main():
    """Run all infrastructure tests"""
    print("🚀 RAG Chatbot Infrastructure Test")
    print("=" * 50)
    
    tests = [
        ("Redis", test_redis),
        ("Milvus Connection", test_milvus),
        ("MinIO", test_minio),
        ("Milvus Health", test_milvus_health),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}: Unexpected error - {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("📊 Test Summary")
    print("=" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All infrastructure services are working correctly!")
        print("🔗 You can now access:")
        print("   - Redis: localhost:6380")
        print("   - Milvus: localhost:19531")
        print("   - MinIO Console: http://localhost:9003")
        print("   - Milvus Health: http://localhost:9092/healthz")
        return True
    else:
        print("⚠️  Some services are not working. Please check the Docker containers.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
