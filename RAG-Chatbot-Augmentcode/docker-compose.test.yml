version: '3.8'

services:
  # Redis for testing
  redis-test:
    image: redis:7-alpine
    container_name: rag-redis-test
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - test-network

  # Milvus components for testing
  etcd-test:
    image: quay.io/coreos/etcd:v3.5.5
    container_name: milvus-etcd-test
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_test_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - test-network

  minio-test:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    container_name: milvus-minio-test
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - minio_test_data:/data
    command: minio server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - test-network

  milvus-test:
    image: milvusdb/milvus:v2.3.0
    container_name: milvus-standalone-test
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd-test:2379
      MINIO_ADDRESS: minio-test:9000
    volumes:
      - milvus_test_data:/var/lib/milvus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    ports:
      - "19531:19530"
      - "9092:9091"
    depends_on:
      - "etcd-test"
      - "minio-test"
    networks:
      - test-network

  # RAG Chatbot API for testing
  api-test:
    build:
      context: .
      dockerfile: chatbot-engine/docker/Dockerfile.api
      target: production
    container_name: rag-chatbot-api-test
    ports:
      - "8001:8000"
    environment:
      - ENVIRONMENT=test
      - MILVUS_HOST=milvus-test
      - MILVUS_PORT=19530
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
      - DEBUG=false
      - PYTHONPATH=/app
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - COHERE_API_KEY=${COHERE_API_KEY}
      - SECRET_KEY=test_secret_key_for_testing_only
      - ADMIN_API_KEY=test_admin_key
    volumes:
      - ./chatbot-engine/data:/app/chatbot-engine/data:ro
      - ./chatbot-engine/config:/app/chatbot-engine/config:ro
      - test_logs:/app/chatbot-engine/logs
    depends_on:
      milvus-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - test-network

  # RAG Chatbot Worker for testing
  worker-test:
    build:
      context: .
      dockerfile: chatbot-engine/docker/Dockerfile.worker
      target: production
    container_name: rag-chatbot-worker-test
    environment:
      - ENVIRONMENT=test
      - MILVUS_HOST=milvus-test
      - MILVUS_PORT=19530
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
      - PYTHONPATH=/app
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - COHERE_API_KEY=${COHERE_API_KEY}
      - SECRET_KEY=test_secret_key_for_testing_only
      - C_FORCE_ROOT=1
    volumes:
      - ./chatbot-engine/data:/app/chatbot-engine/data
      - ./chatbot-engine/config:/app/chatbot-engine/config:ro
      - test_logs:/app/chatbot-engine/logs
    depends_on:
      milvus-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "celery", "-A", "chatbot-engine.src.shared.celery_app", "inspect", "ping", "-d", "celery@$(hostname)"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 60s
    networks:
      - test-network

  # Test runner container
  test-runner:
    build:
      context: .
      dockerfile: chatbot-engine/docker/Dockerfile.api
      target: production
    container_name: rag-test-runner
    environment:
      - ENVIRONMENT=test
      - MILVUS_HOST=milvus-test
      - MILVUS_PORT=19530
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
      - PYTHONPATH=/app
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - COHERE_API_KEY=${COHERE_API_KEY}
      - SECRET_KEY=test_secret_key_for_testing_only
      - API_BASE_URL=http://api-test:8000
    volumes:
      - ./tests:/app/tests
      - ./chatbot-engine:/app/chatbot-engine
      - test_results:/app/test-results
    command: ["sleep", "infinity"]
    depends_on:
      api-test:
        condition: service_healthy
      worker-test:
        condition: service_healthy
    networks:
      - test-network

volumes:
  etcd_test_data:
    driver: local
  minio_test_data:
    driver: local
  milvus_test_data:
    driver: local
  test_logs:
    driver: local
  test_results:
    driver: local

networks:
  test-network:
    driver: bridge
