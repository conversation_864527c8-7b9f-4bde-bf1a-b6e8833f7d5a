# Secrets Management with External Secrets Operator

# External Secrets Operator - SecretStore for HashiCorp Vault
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: vault-secret-store
  namespace: rag-chatbot
spec:
  provider:
    vault:
      server: "https://vault.yourdomain.com"
      path: "secret"
      version: "v2"
      auth:
        kubernetes:
          mountPath: "kubernetes"
          role: "rag-chatbot-role"
          serviceAccountRef:
            name: "rag-chatbot-sa"
---
# External Secrets Operator - SecretStore for AWS Secrets Manager
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: aws-secret-store
  namespace: rag-chatbot
spec:
  provider:
    aws:
      service: SecretsManager
      region: us-west-2
      auth:
        jwt:
          serviceAccountRef:
            name: rag-chatbot-sa
---
# External Secret for API Keys
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: api-keys
  namespace: rag-chatbot
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: vault-secret-store
    kind: SecretStore
  target:
    name: rag-chatbot-api-keys
    creationPolicy: Owner
    template:
      type: Opaque
      data:
        GOOGLE_API_KEY: "{{ .google_api_key }}"
        COHERE_API_KEY: "{{ .cohere_api_key }}"
        SECRET_KEY: "{{ .secret_key }}"
        ADMIN_API_KEY: "{{ .admin_api_key }}"
  data:
  - secretKey: google_api_key
    remoteRef:
      key: rag-chatbot/api-keys
      property: google_api_key
  - secretKey: cohere_api_key
    remoteRef:
      key: rag-chatbot/api-keys
      property: cohere_api_key
  - secretKey: secret_key
    remoteRef:
      key: rag-chatbot/api-keys
      property: secret_key
  - secretKey: admin_api_key
    remoteRef:
      key: rag-chatbot/api-keys
      property: admin_api_key
---
# External Secret for Database Credentials
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: database-credentials
  namespace: rag-chatbot
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: vault-secret-store
    kind: SecretStore
  target:
    name: rag-chatbot-db-credentials
    creationPolicy: Owner
    template:
      type: Opaque
      data:
        REDIS_PASSWORD: "{{ .redis_password }}"
        MILVUS_USER: "{{ .milvus_user }}"
        MILVUS_PASSWORD: "{{ .milvus_password }}"
        MINIO_ACCESS_KEY: "{{ .minio_access_key }}"
        MINIO_SECRET_KEY: "{{ .minio_secret_key }}"
  data:
  - secretKey: redis_password
    remoteRef:
      key: rag-chatbot/database
      property: redis_password
  - secretKey: milvus_user
    remoteRef:
      key: rag-chatbot/database
      property: milvus_user
  - secretKey: milvus_password
    remoteRef:
      key: rag-chatbot/database
      property: milvus_password
  - secretKey: minio_access_key
    remoteRef:
      key: rag-chatbot/database
      property: minio_access_key
  - secretKey: minio_secret_key
    remoteRef:
      key: rag-chatbot/database
      property: minio_secret_key
---
# Sealed Secret for sensitive configuration (alternative approach)
apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  name: rag-chatbot-sealed-secrets
  namespace: rag-chatbot
spec:
  encryptedData:
    # These would be encrypted with kubeseal
    GOOGLE_API_KEY: AgBy3i4OJSWK+PiTySYZZA9rO43cGDEQAx...
    COHERE_API_KEY: AgBy3i4OJSWK+PiTySYZZA9rO43cGDEQAx...
    SECRET_KEY: AgBy3i4OJSWK+PiTySYZZA9rO43cGDEQAx...
  template:
    metadata:
      name: rag-chatbot-sealed-secrets
      namespace: rag-chatbot
    type: Opaque
---
# Secret rotation job
apiVersion: batch/v1
kind: CronJob
metadata:
  name: secret-rotation
  namespace: rag-chatbot
spec:
  schedule: "0 2 * * 0"  # Weekly on Sunday at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: rag-chatbot-sa
          containers:
          - name: secret-rotator
            image: vault:latest
            command:
            - /bin/sh
            - -c
            - |
              # Rotate secrets in Vault
              vault auth -method=kubernetes role=rag-chatbot-role
              
              # Generate new API keys (example)
              NEW_SECRET_KEY=$(openssl rand -base64 32)
              NEW_ADMIN_KEY=$(openssl rand -base64 24)
              
              # Update secrets in Vault
              vault kv put secret/rag-chatbot/api-keys \
                secret_key="$NEW_SECRET_KEY" \
                admin_api_key="$NEW_ADMIN_KEY"
              
              # Trigger secret refresh
              kubectl annotate externalsecret api-keys force-sync=$(date +%s) -n rag-chatbot
            env:
            - name: VAULT_ADDR
              value: "https://vault.yourdomain.com"
          restartPolicy: OnFailure
---
# Secret scanning and compliance
apiVersion: v1
kind: ConfigMap
metadata:
  name: secret-scanner-config
  namespace: rag-chatbot
data:
  scan-rules.yaml: |
    rules:
      - name: "API Key Pattern"
        pattern: "(?i)(api[_-]?key|apikey)\\s*[:=]\\s*['\"]?([a-zA-Z0-9]{20,})['\"]?"
        severity: "HIGH"
      - name: "Secret Key Pattern"
        pattern: "(?i)(secret[_-]?key|secretkey)\\s*[:=]\\s*['\"]?([a-zA-Z0-9]{20,})['\"]?"
        severity: "HIGH"
      - name: "Password Pattern"
        pattern: "(?i)password\\s*[:=]\\s*['\"]?([^\\s'\"]{8,})['\"]?"
        severity: "MEDIUM"
      - name: "Token Pattern"
        pattern: "(?i)token\\s*[:=]\\s*['\"]?([a-zA-Z0-9]{20,})['\"]?"
        severity: "HIGH"
---
# Secret access audit
apiVersion: audit.k8s.io/v1
kind: Policy
metadata:
  name: secret-audit-policy
rules:
- level: Metadata
  resources:
  - group: ""
    resources: ["secrets"]
  namespaces: ["rag-chatbot"]
- level: RequestResponse
  resources:
  - group: ""
    resources: ["secrets"]
  verbs: ["get", "list", "create", "update", "patch", "delete"]
  namespaces: ["rag-chatbot"]
---
# Secret backup and recovery
apiVersion: batch/v1
kind: CronJob
metadata:
  name: secret-backup
  namespace: rag-chatbot
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: rag-chatbot-sa
          containers:
          - name: secret-backup
            image: bitnami/kubectl:latest
            command:
            - /bin/sh
            - -c
            - |
              # Backup secrets to encrypted storage
              kubectl get secrets -n rag-chatbot -o yaml > /backup/secrets-$(date +%Y%m%d).yaml
              
              # Encrypt backup
              gpg --symmetric --cipher-algo AES256 /backup/secrets-$(date +%Y%m%d).yaml
              
              # Upload to secure storage (S3, etc.)
              # aws s3 cp /backup/secrets-$(date +%Y%m%d).yaml.gpg s3://backup-bucket/secrets/
              
              # Cleanup old backups (keep 30 days)
              find /backup -name "secrets-*.yaml.gpg" -mtime +30 -delete
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: secret-backup-pvc
          restartPolicy: OnFailure
