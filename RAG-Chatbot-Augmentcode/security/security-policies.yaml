# Kubernetes Security Policies for RAG Legal Chatbot

apiVersion: v1
kind: Namespace
metadata:
  name: rag-chatbot
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# Pod Security Policy (if PSP is enabled)
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: rag-chatbot-psp
  namespace: rag-chatbot
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    rule: 'MustRunAsNonRoot'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  readOnlyRootFilesystem: true
  seLinux:
    rule: 'RunAsAny'
---
# Network Policy - Default Deny All
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: rag-chatbot
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
# Network Policy - Allow API to Database
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-to-database
  namespace: rag-chatbot
spec:
  podSelector:
    matchLabels:
      app: rag-legal-chatbot
      component: api
  policyTypes:
  - Egress
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: rag-legal-chatbot
          component: redis
    ports:
    - protocol: TCP
      port: 6379
  - to:
    - podSelector:
        matchLabels:
          app: rag-legal-chatbot
          component: milvus
    ports:
    - protocol: TCP
      port: 19530
    - protocol: TCP
      port: 9091
---
# Network Policy - Allow Worker to Database
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: worker-to-database
  namespace: rag-chatbot
spec:
  podSelector:
    matchLabels:
      app: rag-legal-chatbot
      component: worker
  policyTypes:
  - Egress
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: rag-legal-chatbot
          component: redis
    ports:
    - protocol: TCP
      port: 6379
  - to:
    - podSelector:
        matchLabels:
          app: rag-legal-chatbot
          component: milvus
    ports:
    - protocol: TCP
      port: 19530
---
# Network Policy - Allow Ingress to API
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ingress-to-api
  namespace: rag-chatbot
spec:
  podSelector:
    matchLabels:
      app: rag-legal-chatbot
      component: api
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: rag-legal-chatbot
          component: nginx
    ports:
    - protocol: TCP
      port: 8000
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
---
# Network Policy - Allow External API Calls
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: external-api-calls
  namespace: rag-chatbot
spec:
  podSelector:
    matchLabels:
      app: rag-legal-chatbot
      component: api
  policyTypes:
  - Egress
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS
    - protocol: TCP
      port: 53   # DNS
    - protocol: UDP
      port: 53   # DNS
---
# RBAC - Service Account
apiVersion: v1
kind: ServiceAccount
metadata:
  name: rag-chatbot-sa
  namespace: rag-chatbot
automountServiceAccountToken: false
---
# RBAC - Role
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: rag-chatbot
  name: rag-chatbot-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
---
# RBAC - Role Binding
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: rag-chatbot-rolebinding
  namespace: rag-chatbot
subjects:
- kind: ServiceAccount
  name: rag-chatbot-sa
  namespace: rag-chatbot
roleRef:
  kind: Role
  name: rag-chatbot-role
  apiGroup: rbac.authorization.k8s.io
---
# Security Context Constraints (OpenShift)
apiVersion: security.openshift.io/v1
kind: SecurityContextConstraints
metadata:
  name: rag-chatbot-scc
allowHostDirVolumePlugin: false
allowHostIPC: false
allowHostNetwork: false
allowHostPID: false
allowHostPorts: false
allowPrivilegedContainer: false
allowedCapabilities: null
defaultAddCapabilities: null
requiredDropCapabilities:
- ALL
fsGroup:
  type: MustRunAs
  ranges:
  - min: 1000
    max: 65535
readOnlyRootFilesystem: true
runAsUser:
  type: MustRunAsNonRoot
seLinuxContext:
  type: MustRunAs
supplementalGroups:
  type: MustRunAs
  ranges:
  - min: 1000
    max: 65535
volumes:
- configMap
- downwardAPI
- emptyDir
- persistentVolumeClaim
- projected
- secret
users:
- system:serviceaccount:rag-chatbot:rag-chatbot-sa
