#!/usr/bin/env python3
"""
Simple test API to demonstrate RAG chatbot infrastructure is working
"""

import os
import redis
import google.generativeai as genai
import cohere
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from pymilvus import connections, utility, Collection, FieldSchema, CollectionSchema, DataType
import uvicorn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="RAG Chatbot Test API",
    description="Test API to verify infrastructure and API keys are working",
    version="1.0.0"
)

# Request/Response models
class ChatRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str
    status: str
    infrastructure_status: dict

# Initialize services
def init_services():
    """Initialize all services and connections"""
    services_status = {}
    
    # Test Redis
    try:
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        services_status['redis'] = 'connected'
    except Exception as e:
        services_status['redis'] = f'error: {str(e)}'
    
    # Test Milvus
    try:
        connections.connect("default", host="localhost", port="19530")
        if connections.has_connection("default"):
            services_status['milvus'] = 'connected'
        else:
            services_status['milvus'] = 'connection failed'
    except Exception as e:
        services_status['milvus'] = f'error: {str(e)}'
    
    # Test Google AI
    try:
        google_api_key = os.getenv('GOOGLE_API_KEY')
        if google_api_key and google_api_key != 'your_google_ai_api_key_here':
            genai.configure(api_key=google_api_key)
            # Test with a simple generation
            model = genai.GenerativeModel('gemini-1.5-flash')
            response = model.generate_content("Say 'Hello from Google AI!'")
            services_status['google_ai'] = 'connected'
        else:
            services_status['google_ai'] = 'api key not configured'
    except Exception as e:
        services_status['google_ai'] = f'error: {str(e)}'
    
    # Test Cohere
    try:
        cohere_api_key = os.getenv('COHERE_API_KEY')
        if cohere_api_key and cohere_api_key != 'your_cohere_api_key_here':
            co = cohere.Client(cohere_api_key)
            # Test with a simple generation
            response = co.generate(
                model='command',
                prompt='Say "Hello from Cohere!"',
                max_tokens=10
            )
            services_status['cohere'] = 'connected'
        else:
            services_status['cohere'] = 'api key not configured'
    except Exception as e:
        services_status['cohere'] = f'error: {str(e)}'
    
    return services_status

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "RAG Chatbot Test API is running!"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    services_status = init_services()
    
    all_healthy = all(
        status == 'connected' 
        for status in services_status.values()
    )
    
    return {
        "status": "healthy" if all_healthy else "partial",
        "services": services_status
    }

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Simple chat endpoint that tests the infrastructure"""
    
    # Get infrastructure status
    services_status = init_services()
    
    try:
        # Use Google AI to generate a response
        google_api_key = os.getenv('GOOGLE_API_KEY')
        if google_api_key and google_api_key != 'your_google_ai_api_key_here':
            genai.configure(api_key=google_api_key)
            model = genai.GenerativeModel('gemini-1.5-flash')
            
            prompt = f"""
            You are a helpful AI assistant for a RAG (Retrieval-Augmented Generation) chatbot.
            The user asked: "{request.message}"
            
            Please provide a helpful response. Also mention that this is a test of the RAG infrastructure.
            """
            
            response = model.generate_content(prompt)
            ai_response = response.text
        else:
            ai_response = f"Echo: {request.message} (Google AI not configured)"
        
        # Store in Redis cache (optional)
        try:
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.setex(f"chat:{request.message[:50]}", 3600, ai_response)
        except:
            pass  # Cache is optional
        
        return ChatResponse(
            response=ai_response,
            status="success",
            infrastructure_status=services_status
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating response: {str(e)}")

@app.get("/test-vector-db")
async def test_vector_db():
    """Test vector database operations"""
    try:
        # Connect to Milvus
        connections.connect("default", host="localhost", port="19530")
        
        # List existing collections
        collections = utility.list_collections()
        
        # Create a simple test collection if it doesn't exist
        collection_name = "test_collection"
        if collection_name not in collections:
            # Define schema
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=1000),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=128)
            ]
            schema = CollectionSchema(fields, "Test collection for RAG chatbot")
            
            # Create collection
            collection = Collection(collection_name, schema)
            
            return {
                "status": "success",
                "message": f"Created test collection '{collection_name}'",
                "collections": utility.list_collections()
            }
        else:
            return {
                "status": "success", 
                "message": f"Collection '{collection_name}' already exists",
                "collections": collections
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Vector DB error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting RAG Chatbot Test API...")
    print("📋 This API will test:")
    print("   - Redis connection")
    print("   - Milvus vector database")
    print("   - Google AI API")
    print("   - Cohere API")
    print("\n🔗 Access points:")
    print("   - API: http://localhost:8000")
    print("   - Docs: http://localhost:8000/docs")
    print("   - Health: http://localhost:8000/health")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
